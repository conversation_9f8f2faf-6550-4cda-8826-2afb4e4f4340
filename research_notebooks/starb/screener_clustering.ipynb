#%%
import logging
# This is necessary to recognize the modules
import os
import sys
from decimal import Decimal
import warnings

warnings.filterwarnings("ignore")
logging.getLogger("asyncio").setLevel(logging.CRITICAL)
root_path = os.path.abspath(os.path.join(os.getcwd(), '../..'))
sys.path.append(root_path)
#%%
from core.data_sources.clob import CLOBDataSource

clob = CLOBDataSource()
clob.load_candles_cache(root_path)
#%%
# available_candles = await ts_client.get_available_candles()
# print(available_candles)
# trading_rules = await clob.get_trading_rules(connector_name='binance_perpetual')

#%%

# candles = await clob.get_candles_batch_last_days(connector_name='binance_perpetual', 
#                                                  trading_pairs=trading_rules.get_all_trading_pairs(), 
#                                                  interval='15m', 
#                                                  days=14, batch_size=20, sleep_time=5.0)
# clob.dump_candles_cache(root_path)
#%%
candles = list(clob.candles_cache.values())
#%%
import pandas as pd

# summarize the volumes
volumes = [candle.data['quote_asset_volume'].median() for candle in candles]
volumes_df = pd.DataFrame(volumes, index=[candle.trading_pair for candle in candles])
volumes_df.sort_values(by=0, ascending=False)

# filter the pairs by a quantile X of the volumes and plot the pairs
quantile = 0.75
filtered_pairs = volumes_df[volumes_df[0] > volumes_df[0].quantile(quantile)]
filtered_pairs.sort_values(by=0, ascending=False)


#%%
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import coint
from sklearn.linear_model import LinearRegression

def cointegration_analysis(y_col, x_col):
    """
    Perform cointegration analysis between two series.
    
    Args:
        y_col (pd.Series): The Y series.
        x_col (pd.Series): The X series.
    
    Returns:
        dict: A dictionary containing p-value, alpha, beta, and Z_t values.
    """
    # Drop NaN or Infinite values from both series
    y_col = y_col.dropna()
    x_col = x_col.dropna()

    # Ensure there are no infinite values
    y_col = y_col[np.isfinite(y_col)]
    x_col = x_col[np.isfinite(x_col)]

    # Ensure both series are of the same length after dropping NaNs
    min_len = min(len(y_col), len(x_col))
    y_col = y_col[-min_len:]
    x_col = x_col[-min_len:]

    y, x = y_col.values, x_col.values

    # Run the Engle-Granger test for cointegration
    coint_res = coint(y, x)
    p_value = coint_res[1]
    
    # Perform linear regression to find alpha and beta
    x_reshaped = x.reshape(-1, 1)  # Reshape for sklearn
    reg = LinearRegression().fit(x_reshaped, y)
    alpha = reg.intercept_
    beta = reg.coef_[0]
    
    # Calculate Z_t = Y - (alpha + beta * X)
    z_t = y - (alpha + beta * x)
    median_z = np.median(z_t)
    
    return {
        'P-Value': p_value,
        'Alpha': alpha,
        'Beta': beta,
        'Median Z': median_z,
        'Z_t': z_t
    }

def cointegration_error_analysis(y_col, x_col, n_days, interval_min = 15):
    """
    Calculate the median percentage error between predicted and actual values 
    for the last n days based on the full-series cointegration model (alpha and beta).
    
    Args:
        y_col (pd.Series): The Y series (dependent variable).
        x_col (pd.Series): The X series (independent variable).
        n_days (int): Number of most recent days to use for prediction and error calculation.
    
    Returns:
        dict: A dictionary containing median percentage error, strategy recommendation, and other details.
    """
    # Perform cointegration analysis using the **entire series**
    result = cointegration_analysis(y_col, x_col)
    
    alpha = result['Alpha']
    beta = result['Beta']
    last_records = int(n_days * (60 / interval_min) * 24)

    y_recent = y_col.tail(last_records)
    x_recent = x_col.tail(last_records)
    
    # Predict using the cointegration formula: Y_t = alpha + beta * X_t
    y_pred = alpha + beta * x_recent
    
    # Calculate the percentage error for each prediction
    percentage_error = ((y_pred - y_recent) / y_recent.abs()) * 100
    
    # Calculate the median percentage error
    median_error = np.median(percentage_error)
    
    # Determine the strategy based on the median error
    strategy = "Hold"  # Default to Hold
    
    if median_error > 0:
        strategy = "Long"  # Predicted > Actual → Actual expected to rise
    elif median_error < 0:
        strategy = "Short"  # Predicted < Actual → Actual expected to fall
    
    # Return the results
    return {
        "P-Value": result['P-Value'],
        "Median Z": result['Median Z'],
        "Median Percentage Error": median_error,
        "Strategy": strategy,
        "Predictions": y_pred,
        "Actual Values": y_recent,
        "Percentage Errors": percentage_error,
        "Alpha": alpha,
        "Beta": beta,
        "Cointegration Result": result
    }

#%%
import plotly.graph_objects as go

# Example Data for Testing
x_candle = next(candle for candle in candles if candle.trading_pair == "ETH-USDT")
y_candle = next(candle for candle in candles if candle.trading_pair == "BTC-USDT")
# Apply strategy for the last n days
n_days = 7
n_days_to_predict = 3

# filter candles to only include the last n days
x_col = x_candle.data.dropna()[-n_days*24*60//15:]["close"].pct_change().add(1).cumprod()
y_col = y_candle.data.dropna()[-n_days*24*60//15:]["close"].pct_change().add(1).cumprod()


result_y_x = cointegration_error_analysis(y_col, x_col, n_days_to_predict)
result_x_y = cointegration_error_analysis(x_col, y_col, n_days_to_predict)

# Print results
print(f"Median Percentage Error: {result_y_x['Median Percentage Error']}")
print("P-Value: ", result_y_x['P-Value'])
print(f"Strategy: {result_y_x['Strategy']}")
print(f"Alpha: {result_y_x['Alpha']}, Beta: {result_y_x['Beta']}")
print(f"Median Percentage Error: {result_x_y['Median Percentage Error']}")
print(f"Strategy: {result_x_y['Strategy']}")
print(f"Alpha: {result_x_y['Alpha']}, Beta: {result_x_y['Beta']}")

# Create figure with plotly
fig = go.Figure()

# Plot all actual values (entire series)
fig.add_trace(
    go.Scatter(
        x=y_col.index,
        y=y_col,
        name="Y",
        line=dict(color='blue')
    )
)

fig.add_trace(
    go.Scatter(
        x=x_col.index,
        y=x_col,
        name="X",
        line=dict(color='red')
    )
)

# Overlay the predicted values for last n days
fig.add_trace(
    go.Scatter(
        x=result_y_x["Actual Values"].index,
        y=result_y_x["Predictions"],
        name="Predicted Values (y --> x)", 
        line=dict(color='blue', dash='dash')
    )
)


fig.add_trace(
    go.Scatter(
        x=result_x_y["Actual Values"].index,
        y=result_x_y["Predictions"],
        name="Predicted Values (x --> y)", 
        line=dict(color='red', dash='dash')
    )
)

# Update layout
fig.update_layout(
    title=f"Actual vs Predicted Values (Last {n_days} Days)",
    xaxis_title="Date",
    yaxis_title="Value",
    width=1200,
    height=600
)

fig.show()


#%%
def analyze_all_pairs(candles, n_days=3):
    """
    Analyze all possible pairs to find cointegrated pairs with recent divergence.
    
    Args:
        candles: List of candle objects
        n_days: Number of days to look back for divergence
        p_value_threshold: Maximum p-value to consider pairs cointegrated
        min_error_threshold: Minimum percentage error to consider divergence significant
    
    Returns:
        DataFrame with analysis results
    """
    results = []
    
    # Create all possible pairs combinations
    for i, candle1 in enumerate(candles):
        for j, candle2 in enumerate(candles[i+1:], i+1):
            if candle1.data is None or candle2.data is None:
                continue
                
            # Get normalized price series
            y_col = candle1.data["close"].dropna().pct_change().add(1).cumprod()
            x_col = candle2.data["close"].dropna().pct_change().add(1).cumprod()
            
            # Skip if not enough data
            if len(y_col) < 30 or len(x_col) < 30:
                continue
            
            try:
                # Get recent divergence analysis
                error_result = cointegration_error_analysis(y_col, x_col, n_days)
                
                results.append({
                    'pair_1': candle1.trading_pair,
                    'pair_2': candle2.trading_pair,
                    'p_value': error_result['P-Value'],
                    'alpha': error_result['Alpha'],
                    'beta': error_result['Beta'],
                    'median_z': error_result['Median Z'],
                    'strategy': error_result['Strategy'],
                    'error_pct': error_result['Median Percentage Error'],
                    'score': abs(error_result['Median Percentage Error']) * (1 - error_result['P-Value'])
                })
            except Exception as e:
                print(f"Error analyzing {candle1.trading_pair} vs {candle2.trading_pair}: {str(e)}")
                continue
    
    # Convert to DataFrame and filter
    df = pd.DataFrame(results)
    
    # Sort by score (higher is better)
    df = df.sort_values('score', ascending=False)
    
    return df


candles_filtered = [candle for candle in candles if candle.data is not None and candle.trading_pair in filtered_pairs.index]
# Run the analysis
results_df = analyze_all_pairs(candles_filtered, n_days=3)

# Display top long opportunities
print("\nTop Long Opportunities:")
print(results_df[results_df['strategy'] == 'Long'].head(10))

# Display top short opportunities
print("\nTop Short Opportunities:")
print(results_df[results_df['strategy'] == 'Short'].head(10))

# Optional: Visualize top pair
def plot_top_pair(results_df, candles_dict, position='Long'):
    """Plot the top pair for given position"""
    top_pair = results_df[results_df['strategy'] == position].iloc[0]
    
    y_col = candles_dict[top_pair['pair_1']]["close"].dropna().pct_change().add(1).cumprod()
    x_col = candles_dict[top_pair['pair_2']]["close"].dropna().pct_change().add(1).cumprod()
    
    result = cointegration_error_analysis(y_col, x_col, n_days=5)
    
    fig = go.Figure()
    
    # Plot actual values
    fig.add_trace(go.Scatter(
        x=y_col.index, y=y_col,
        name=top_pair['pair_1'],
        line=dict(color='blue')
    ))
    
    fig.add_trace(go.Scatter(
        x=x_col.index, y=x_col,
        name=top_pair['pair_2'],
        line=dict(color='red')
    ))
    
    # Plot predictions
    fig.add_trace(go.Scatter(
        x=result["Actual Values"].index,
        y=result["Predictions"],
        name="Predicted Values",
        line=dict(color='green', dash='dash')
    ))
    
    fig.update_layout(
        title=f"Top {position} Opportunity: {top_pair['pair_1']} vs {top_pair['pair_2']}",
        xaxis_title="Date",
        yaxis_title="Normalized Price",
        width=1200,
        height=600
    )
    
    fig.show()

# Create dictionary of candles for easier access
candles_dict = {candle.trading_pair: candle.data for candle in candles}

# Plot top opportunities
plot_top_pair(results_df, candles_dict, 'Long')
plot_top_pair(results_df, candles_dict, 'Short')
#%%
# plot all the pairs and the p-values in the x axis and volume in the y axis obtained from the results_df and the candles

results_df[results_df["p_value"] < 0.02]
#%%
# First, let's calculate median volume for each trading pair
volume_medians = {}
for candle in candles:
    if candle.data is not None:
        volume_medians[candle.trading_pair] = candle.data['volume'].median()

# Calculate the 50th percentile of volumes
volume_threshold = np.percentile(list(volume_medians.values()), 80)

# Filter results_df to only include high volume pairs
high_volume_results = results_df[
    (results_df['pair_1'].map(volume_medians) >= volume_threshold) & 
    (results_df['pair_2'].map(volume_medians) >= volume_threshold) &
    (results_df['p_value'] < 0.05)
]

def plot_multiple_pairs(results_df, candles_dict, position='Long', n_pairs=10):
    """Plot multiple top pairs for given position"""
    top_pairs = results_df[results_df['strategy'] == position].head(n_pairs)
    
    for _, pair in top_pairs.iterrows():
        y_col = candles_dict[pair['pair_1']]["close"].dropna().pct_change().add(1).cumprod()
        x_col = candles_dict[pair['pair_2']]["close"].dropna().pct_change().add(1).cumprod()
        
        result = cointegration_error_analysis(y_col, x_col, n_days=1)
        
        fig = go.Figure()
        
        # Plot actual values
        fig.add_trace(go.Scatter(
            x=y_col.index, y=y_col,
            name=f"{pair['pair_1']} (Vol: {volume_medians[pair['pair_1']]:,.0f})",
            line=dict(color='blue')
        ))
        
        fig.add_trace(go.Scatter(
            x=x_col.index, y=x_col,
            name=f"{pair['pair_2']} (Vol: {volume_medians[pair['pair_2']]:,.0f})",
            line=dict(color='red')
        ))
        
        # Plot predictions
        fig.add_trace(go.Scatter(
            x=result["Actual Values"].index,
            y=result["Predictions"],
            name="Predicted Values",
            line=dict(color='green', dash='dash')
        ))
        
        fig.update_layout(
            title=f"{position} Opportunity: {pair['pair_1']} vs {pair['pair_2']}<br>" + 
                  f"Error: {pair['error_pct']:.2f}%, P-value: {pair['p_value']:.4f}, Score: {pair['score']:.2f}",
            xaxis_title="Date",
            yaxis_title="Normalized Price",
            width=1200,
            height=500,
            showlegend=True
        )
        
        fig.show()

# Print summary of high volume opportunities
print("\nTop Long Opportunities (High Volume):")
print(high_volume_results[high_volume_results['strategy'] == 'Long'].sort_values(by="alpha", ascending=False).head(10))

print("\nTop Short Opportunities (High Volume):")
print(high_volume_results[high_volume_results['strategy'] == 'Short'].sort_values(by="alpha", ascending=False).head(10))

# Plot top opportunities
print("\nVisualizing Top Long Opportunities:")
plot_multiple_pairs(high_volume_results, candles_dict, 'Long', 10)

print("\nVisualizing Top Short Opportunities:")
plot_multiple_pairs(high_volume_results, candles_dict, 'Short', 10)
#%%
# First, let's calculate median volume for each trading pair (reusing existing code)
volume_medians = {}
for candle in candles:
    if candle.data is not None:
        volume_medians[candle.trading_pair] = candle.data['volume'].median()

# Calculate the 50th percentile of volumes
volume_threshold = np.percentile(list(volume_medians.values()), 50)

# Filter results_df to only include high volume pairs
high_volume_results = results_df[
    (results_df['pair_1'].map(volume_medians) >= volume_threshold) & 
    (results_df['pair_2'].map(volume_medians) >= volume_threshold)
]

# Create token-level scoring from the filtered results
token_scores = defaultdict(lambda: {'long_score': 0, 'short_score': 0, 'count': 0})

# Process each pair in high_volume_results
for _, row in high_volume_results.iterrows():
    pair_score = row['score']
    
    if row['strategy'] == 'Long':
        token_scores[row['pair_1']]['long_score'] += pair_score
        token_scores[row['pair_2']]['short_score'] += pair_score
    else:  # Short strategy
        token_scores[row['pair_1']]['short_score'] += pair_score
        token_scores[row['pair_2']]['long_score'] += pair_score
    
    token_scores[row['pair_1']]['count'] += 1
    token_scores[row['pair_2']]['count'] += 1

# Convert token scores to DataFrame
token_df = pd.DataFrame([
    {
        'token': token,
        'long_score': scores['long_score'] / scores['count'] if scores['count'] > 0 else 0,
        'short_score': scores['short_score'] / scores['count'] if scores['count'] > 0 else 0,
        'relationship_count': scores['count'],
        'volume': volume_medians[token]
    }
    for token, scores in token_scores.items()
])

# Sort tokens by their best score (either long or short)
token_df['best_score'] = token_df[['long_score', 'short_score']].max(axis=1)
token_df['preferred_strategy'] = token_df.apply(
    lambda x: 'Long' if x['long_score'] >= x['short_score'] else 'Short', axis=1
)
token_df = token_df.sort_values('best_score', ascending=False)

# Display top tokens for each strategy
print("\nTop Tokens for Long Strategy:")
print(token_df[token_df['preferred_strategy'] == 'Long'].head(10)[
    ['token', 'long_score', 'relationship_count', 'volume']
])

print("\nTop Tokens for Short Strategy:")
print(token_df[token_df['preferred_strategy'] == 'Short'].head(10)[
    ['token', 'short_score', 'relationship_count', 'volume']
])

def plot_top_tokens(token_df, candles_dict, strategy='Long', n_tokens=5):
    """Plot price action for top tokens with given strategy"""
    top_tokens = token_df[token_df['preferred_strategy'] == strategy].head(n_tokens)
    
    for _, token_data in top_tokens.iterrows():
        token = token_data['token']
        price_series = candles_dict[token]["close"].dropna().pct_change().add(1).cumprod()
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=price_series.index,
            y=price_series,
            name=token,
            line=dict(color='blue' if strategy == 'Long' else 'red')
        ))
        
        fig.update_layout(
            title=f"Top {strategy} Token: {token}<br>" + 
                  f"Score: {token_data[f'{strategy.lower()}_score']:.2f}, " + 
                  f"Relationships: {token_data['relationship_count']}, " +
                  f"Volume: {token_data['volume']:,.0f}",
            xaxis_title="Date",
            yaxis_title="Normalized Price",
            width=1200,
            height=400,
            showlegend=True
        )
        
        fig.show()

# Plot top tokens
print("\nVisualizing Top Long Tokens:")
plot_top_tokens(token_df, candles_dict, 'Long')

print("\nVisualizing Top Short Tokens:")
plot_top_tokens(token_df, candles_dict, 'Short')
#%%
def plot_consolidated_tokens(token_df, candles_dict, strategy='Long', n_tokens=5):
    """Plot price action for top tokens with given strategy in a single chart"""
    top_tokens = token_df[token_df['preferred_strategy'] == strategy].head(n_tokens)
    
    fig = go.Figure()
    
    # Color palette for different tokens
    colors = ['blue', 'red', 'green', 'purple', 'orange', 'brown', 'pink', 'gray', 'cyan', 'magenta']
    
    for i, (_, token_data) in enumerate(top_tokens.iterrows()):
        token = token_data['token']
        color = colors[i % len(colors)]
        
        # Get price series
        price_series = candles_dict[token]["close"].dropna().pct_change().add(1).cumprod()
        
        # Calculate predicted values using the last n days
        n_days = 5  # Adjust this value as needed
        last_records = int(n_days * (60 / 15) * 24)  # Assuming 15-minute intervals
        recent_price = price_series.tail(last_records)
        
        # Add actual price line
        fig.add_trace(go.Scatter(
            x=price_series.index,
            y=price_series,
            name=f"{token} (Vol: {token_data['volume']:,.0f})",
            line=dict(color=color)
        ))
        
        # Add predicted line (dashed)
        fig.add_trace(go.Scatter(
            x=recent_price.index,
            y=recent_price * (1 + token_data[f'{strategy.lower()}_score']/100),  # Adjust based on score
            name=f"{token} Target",
            line=dict(color=color, dash='dash')
        ))
    
    fig.update_layout(
        title=f"Top {n_tokens} {strategy} Tokens Consolidated View<br>" + 
              "Solid lines: Actual Prices, Dashed lines: Predicted Targets",
        xaxis_title="Date",
        yaxis_title="Normalized Price",
        width=1200,
        height=800,
        showlegend=True
    )
    
    fig.show()

# Plot consolidated views
print("\nConsolidated View of Top Long Tokens:")
plot_consolidated_tokens(token_df, candles_dict, 'Long', 5)

print("\nConsolidated View of Top Short Tokens:")
plot_consolidated_tokens(token_df, candles_dict, 'Short', 5)
#%%
import plotly.graph_objects as go

PAIR_1 = "BTC-USDT"
PAIR_2 = "LTC-USDT"

# Example Data for Testing
x_candle = next(candle for candle in candles if candle.trading_pair == PAIR_1)
y_candle = next(candle for candle in candles if candle.trading_pair == PAIR_2)
# Apply strategy for the last n days
n_days = 7
n_days_to_predict = 3

# filter candles to only include the last n days
x_col = x_candle.data.dropna()[-n_days*24*60//15:]["close"].pct_change().add(1).cumprod()
y_col = y_candle.data.dropna()[-n_days*24*60//15:]["close"].pct_change().add(1).cumprod()


result_y_x = cointegration_error_analysis(y_col, x_col, n_days_to_predict)
result_x_y = cointegration_error_analysis(x_col, y_col, n_days_to_predict)

# Print results
print(f"Median Percentage Error: {result_y_x['Median Percentage Error']}")
print("P-Value: ", result_y_x['P-Value'])
print(f"Strategy: {result_y_x['Strategy']}")
print(f"Alpha: {result_y_x['Alpha']}, Beta: {result_y_x['Beta']}")
print(f"Median Percentage Error: {result_x_y['Median Percentage Error']}")
print(f"Strategy: {result_x_y['Strategy']}")
print(f"Alpha: {result_x_y['Alpha']}, Beta: {result_x_y['Beta']}")

# Create figure with plotly
fig = go.Figure()

# Plot all actual values (entire series)
fig.add_trace(
    go.Scatter(
        x=y_col.index,
        y=y_col,
        name="Y",
        line=dict(color='blue')
    )
)

fig.add_trace(
    go.Scatter(
        x=x_col.index,
        y=x_col,
        name="X",
        line=dict(color='red')
    )
)

# Overlay the predicted values for last n days
fig.add_trace(
    go.Scatter(
        x=result_y_x["Actual Values"].index,
        y=result_y_x["Predictions"],
        name="Predicted Values (y --> x)", 
        line=dict(color='blue', dash='dash')
    )
)

fig.add_trace(
    go.Scatter(
        x=result_x_y["Actual Values"].index,
        y=result_x_y["Predictions"],
        name="Predicted Values (x --> y)", 
        line=dict(color='red', dash='dash')
    )
)

# Update layout
fig.update_layout(
    title=f"Actual vs Predicted Values (Last {n_days} Days)",
    xaxis_title="Date",
    yaxis_title="Value",
    width=1200,
    height=600
)

fig.show()


#%%
from scipy import stats

def analyze_pair_cointegration(y_col, x_col, lookback_days=14, signal_days=3, z_score_threshold=2.0):
    """
    Comprehensive cointegration analysis combining spread analysis and trading signals.
    
    Args:
        y_col (pd.Series): The Y series (dependent variable)
        x_col (pd.Series): The X series (independent variable)
        lookback_days (int): Days of data to use for cointegration analysis
        signal_days (int): Recent days to analyze for trading signals
        z_score_threshold (float): Z-score threshold for trading signals
    
    Returns:
        dict: Comprehensive analysis results including trading signals
    """
    # Calculate periods for 15m candles
    lookback_periods = lookback_days * 24 * 4  # 15-min candles per day
    signal_periods = signal_days * 24 * 4
    
    # Prepare price series
    y_col = y_col.dropna()
    x_col = x_col.dropna()
    
    # Ensure finite values
    y_col = y_col[np.isfinite(y_col)]
    x_col = x_col[np.isfinite(x_col)]
    
    # Get last n periods for analysis
    y_col = y_col.tail(lookback_periods)
    x_col = x_col.tail(lookback_periods)
    
    # Ensure both series are of the same length
    min_len = min(len(y_col), len(x_col))
    y_col = y_col[-min_len:]
    x_col = x_col[-min_len:]
    
    y, x = y_col.values, x_col.values
    
    # Run Engle-Granger test
    coint_res = coint(y, x)
    p_value = coint_res[1]
    
    # Perform linear regression
    x_reshaped = x.reshape(-1, 1)
    reg = LinearRegression().fit(x_reshaped, y)
    alpha = reg.intercept_
    beta = reg.coef_[0]
    
    # Calculate spread (Z_t)
    z_t = y - (alpha + beta * x)
    z_mean = np.mean(z_t)
    z_std = np.std(z_t)
    
    # Get recent data for signal analysis
    y_recent = y_col.tail(signal_periods)
    x_recent = x_col.tail(signal_periods)
    
    # Calculate recent predictions and spread
    y_pred = alpha + beta * x_recent
    recent_spread = y_recent - y_pred
    
    # Calculate current Z-score
    current_z_score = (z_t[-1] - z_mean) / z_std
    
    # Determine trading strategy
    if abs(current_z_score) < z_score_threshold:
        strategy = "Hold"
    elif current_z_score > z_score_threshold:
        strategy = "Short"  # Y is overvalued
    else:  # current_z_score < -z_score_threshold
        strategy = "Long"   # Y is undervalued
    
    # Calculate additional metrics
    signal_strength = abs(current_z_score) / z_score_threshold
    mean_reversion_prob = 1 - stats.norm.cdf(abs(current_z_score))
    
    # Calculate percentage error for recent period
    percentage_error = ((y_pred - y_recent) / y_recent.abs()) * 100
    median_error = np.median(percentage_error)
    
    return {
        # Cointegration statistics
        'P-Value': p_value,
        'Alpha': alpha,
        'Beta': beta,
        
        # Spread analysis
        'Z_t': z_t,
        'Z_mean': z_mean,
        'Z_std': z_std,
        'Current_Z_score': current_z_score,
        
        # Trading signals
        'Strategy': strategy,
        'Signal_Strength': signal_strength,
        'Mean_Reversion_Probability': mean_reversion_prob,
        
        # Recent performance
        'Recent_Spread': recent_spread,
        'Predictions': y_pred,
        'Actual_Values': y_recent,
        'Median_Error': median_error,
        
        # Risk management
        'Stop_Loss_Z_score': current_z_score * 1.5,  # 50% additional deviation
        'Target_Z_score': 0,  # Mean reversion target
        
        # Trade setup
        'Position_Ratio': beta,
        'Z_score_Threshold': z_score_threshold
    }

def plot_pair_analysis(candle1, candle2, analysis_result):
    """
    Create comprehensive visualization of the pair analysis.
    """
    fig = make_subplots(rows=2, cols=1, 
                       subplot_titles=('Normalized Prices and Prediction', 'Z_t (Spread)'),
                       vertical_spacing=0.15,
                       row_heights=[0.6, 0.4])
    
    # Use trading_pair instead of name
    pair1_name = candle1.trading_pair
    pair2_name = candle2.trading_pair
    
    # Plot 1: Prices and Prediction
    y_col = candle1["close"].pct_change().add(1).cumprod()
    x_col = candle2["close"].pct_change().add(1).cumprod()
    
    fig.add_trace(
        go.Scatter(x=y_col.index, y=y_col, 
                  name=f'Y: {pair1_name}',
                  line=dict(color='blue')), row=1, col=1)
    fig.add_trace(
        go.Scatter(x=x_col.index, y=x_col, 
                  name=f'X: {pair2_name}',
                  line=dict(color='red')), row=1, col=1)
    
    # Add predictions
    fig.add_trace(
        go.Scatter(x=analysis_result['Predictions'].index, 
                  y=analysis_result['Predictions'],
                  name='Predicted Y',
                  line=dict(color='green', dash='dash')), row=1, col=1)
    
    # Plot 2: Z_t Analysis
    z_t = analysis_result['Z_t']
    z_mean = analysis_result['Z_mean']
    z_std = analysis_result['Z_std']
    
    fig.add_trace(
        go.Scatter(y=z_t, name='Z_t (Spread)',
                  line=dict(color='blue')), row=2, col=1)
    
    # Add mean and std bands
    fig.add_trace(
        go.Scatter(y=[z_mean] * len(z_t), name='Mean',
                  line=dict(color='black', dash='dash')), row=2, col=1)
    
    for n_std in [1, 2]:
        fig.add_trace(
            go.Scatter(y=[z_mean + z_std * n_std] * len(z_t),
                      name=f'+{n_std}σ',
                      line=dict(color='red', dash='dot')), row=2, col=1)
        fig.add_trace(
            go.Scatter(y=[z_mean - z_std * n_std] * len(z_t),
                      name=f'-{n_std}σ',
                      line=dict(color='red', dash='dot')), row=2, col=1)
    
    # Update layout with analysis results
    title_text = (
        f"Pair Analysis: {pair1_name} vs {pair2_name}<br>" +
        f"P-Value: {analysis_result['P-Value']:.4f} | " +
        f"Current Z-Score: {analysis_result['Current_Z_score']:.2f} | " +
        f"Strategy: {analysis_result['Strategy']}<br>" +
        f"Signal Strength: {analysis_result['Signal_Strength']:.2f} | " +
        f"Mean Reversion Prob: {analysis_result['Mean_Reversion_Probability']:.2%}"
    )
    
    fig.update_layout(
        title=title_text,
        width=1200,
        height=800,
        showlegend=True
    )
    
    return fig

def analyze_pairs(candles, top_n=5):
    """Analyze all pairs and show top opportunities."""
    results = []
    
    for i, candle1 in enumerate(candles):
        for candle2 in candles[i+1:]:
            try:
                # Get normalized price series
                y_col = candle1.data["close"].pct_change().add(1).cumprod()
                x_col = candle2.data["close"].pct_change().add(1).cumprod()
                
                # Perform analysis
                analysis = analyze_pair_cointegration(y_col, x_col)
                
                if analysis['P-Value'] < 0.05:  # Only store cointegrated pairs
                    results.append({
                        'pair': (candle1, candle2),
                        'analysis': analysis
                    })
            except Exception as e:
                print(f"Error analyzing {candle1.trading_pair} vs {candle2.trading_pair}: {str(e)}")
                continue
    
    # Sort by signal strength
    results.sort(key=lambda x: x['analysis']['Signal_Strength'], reverse=True)
    
    # Display top opportunities
    for i, result in enumerate(results[:top_n]):
        candle1, candle2 = result['pair']
        analysis = result['analysis']
        
        print(f"\n{i+1}. {candle1.trading_pair} vs {candle2.trading_pair}")
        print(f"Strategy: {analysis['Strategy']}")
        print(f"Z-Score: {analysis['Current_Z_score']:.2f}")
        print(f"Signal Strength: {analysis['Signal_Strength']:.2f}")
        print(f"Mean Reversion Probability: {analysis['Mean_Reversion_Probability']:.2%}")
        print(f"Position Ratio (β): {analysis['Position_Ratio']:.4f}")
        
        # Plot analysis
        fig = plot_pair_analysis(candle1.data, candle2.data, analysis)
        fig.show()

# Run the analysis
analyze_pairs(candles, top_n=5)
#%%
# import make_subplots
from plotly.subplots import make_subplots

def plot_pair_analysis(candle1, candle2, analysis_result):
    """
    Create comprehensive visualization of the pair analysis.
    """
    fig = make_subplots(rows=2, cols=1, 
                       subplot_titles=('Normalized Prices and Prediction', 'Z_t (Spread)'),
                       vertical_spacing=0.15,
                       row_heights=[0.6, 0.4])
    
    # Use trading_pair instead of name
    pair1_name = candle1.trading_pair
    pair2_name = candle2.trading_pair
    
    # Plot 1: Prices and Prediction
    y_col = candle1["close"].pct_change().add(1).cumprod()
    x_col = candle2["close"].pct_change().add(1).cumprod()
    
    fig.add_trace(
        go.Scatter(x=y_col.index, y=y_col, 
                  name=f'Y: {pair1_name}',
                  line=dict(color='blue')), row=1, col=1)
    fig.add_trace(
        go.Scatter(x=x_col.index, y=x_col, 
                  name=f'X: {pair2_name}',
                  line=dict(color='red')), row=1, col=1)
    
    # Add predictions
    fig.add_trace(
        go.Scatter(x=analysis_result['Predictions'].index, 
                  y=analysis_result['Predictions'],
                  name='Predicted Y',
                  line=dict(color='green', dash='dash')), row=1, col=1)
    
    # Plot 2: Z_t Analysis
    z_t = analysis_result['Z_t']
    z_mean = analysis_result['Z_mean']
    z_std = analysis_result['Z_std']
    
    fig.add_trace(
        go.Scatter(y=z_t, name='Z_t (Spread)',
                  line=dict(color='blue')), row=2, col=1)
    
    # Add mean and std bands
    fig.add_trace(
        go.Scatter(y=[z_mean] * len(z_t), name='Mean',
                  line=dict(color='black', dash='dash')), row=2, col=1)
    
    for n_std in [1, 2]:
        fig.add_trace(
            go.Scatter(y=[z_mean + z_std * n_std] * len(z_t),
                      name=f'+{n_std}σ',
                      line=dict(color='red', dash='dot')), row=2, col=1)
        fig.add_trace(
            go.Scatter(y=[z_mean - z_std * n_std] * len(z_t),
                      name=f'-{n_std}σ',
                      line=dict(color='red', dash='dot')), row=2, col=1)
    
    # Update layout with analysis results
    title_text = (
        f"Pair Analysis: {pair1_name} vs {pair2_name}<br>" +
        f"P-Value: {analysis_result['P-Value']:.4f} | " +
        f"Current Z-Score: {analysis_result['Current_Z_score']:.2f} | " +
        f"Strategy: {analysis_result['Strategy']}<br>" +
        f"Signal Strength: {analysis_result['Signal_Strength']:.2f} | " +
        f"Mean Reversion Prob: {analysis_result['Mean_Reversion_Probability']:.2%}"
    )
    
    fig.update_layout(
        title=title_text,
        width=1200,
        height=800,
        showlegend=True
    )
    
    return fig

def analyze_pairs(candles, top_n=5):
    """Analyze all pairs and show top opportunities."""
    results = []
    
    for i, candle1 in enumerate(candles):
        for candle2 in candles[i+1:]:
            try:
                # Get normalized price series
                y_col = candle1.data["close"].pct_change().add(1).cumprod()
                x_col = candle2.data["close"].pct_change().add(1).cumprod()
                
                # Perform analysis
                analysis = analyze_pair_cointegration(y_col, x_col)
                
                if analysis['P-Value'] < 0.05:  # Only store cointegrated pairs
                    results.append({
                        'pair': (candle1, candle2),
                        'analysis': analysis
                    })
            except Exception as e:
                print(f"Error analyzing {candle1.trading_pair} vs {candle2.trading_pair}: {str(e)}")
                continue
    
    # Sort by signal strength
    results.sort(key=lambda x: x['analysis']['Signal_Strength'], reverse=True)
    
    # Display top opportunities
    for i, result in enumerate(results[:top_n]):
        candle1, candle2 = result['pair']
        analysis = result['analysis']
        
        print(f"\n{i+1}. {candle1.trading_pair} vs {candle2.trading_pair}")
        print(f"Strategy: {analysis['Strategy']}")
        print(f"Z-Score: {analysis['Current_Z_score']:.2f}")
        print(f"Signal Strength: {analysis['Signal_Strength']:.2f}")
        print(f"Mean Reversion Probability: {analysis['Mean_Reversion_Probability']:.2%}")
        print(f"Position Ratio (β): {analysis['Position_Ratio']:.4f}")
        
        # Plot analysis
        fig = plot_pair_analysis(candle1.data, candle2.data, analysis)
        fig.show()

# Run the analysis
analyze_pairs(candles, top_n=5)
#%%
from scipy import stats

# Get the specific candles
btc_candle = next(candle for candle in candles if candle.trading_pair == 'BTC-USDT')
ltc_candle = next(candle for candle in candles if candle.trading_pair == 'LTC-USDT')

# Get normalized price series
btc_prices = btc_candle.data["close"].pct_change().add(1).cumprod()
ltc_prices = ltc_candle.data["close"].pct_change().add(1).cumprod()

# Perform analysis
analysis = analyze_pair_cointegration(ltc_prices, btc_prices, lookback_days=14, signal_days=14, z_score_threshold=2.0)

# Print analysis results
print(f"\nAnalysis: LTC-USDT vs BTC-USDT")
print(f"Strategy: {analysis['Strategy']}")
print(f"P-Value: {analysis['P-Value']:.4f}")
print(f"Z-Score: {analysis['Current_Z_score']:.2f}")
print(f"Signal Strength: {analysis['Signal_Strength']:.2f}")
print(f"Mean Reversion Probability: {analysis['Mean_Reversion_Probability']:.2%}")
print(f"Position Ratio (β): {analysis['Position_Ratio']:.4f}")

# Create visualization
fig = make_subplots(rows=2, cols=1, 
                    subplot_titles=('Normalized Prices and Prediction', 'Z_t (Spread)'),
                    vertical_spacing=0.15,
                    row_heights=[0.6, 0.4])

# Plot 1: Prices and Prediction
fig.add_trace(
    go.Scatter(x=ltc_prices.index, y=ltc_prices, 
              name='LTC-USDT',
              line=dict(color='blue')), row=1, col=1)
fig.add_trace(
    go.Scatter(x=btc_prices.index, y=btc_prices, 
              name='BTC-USDT',
              line=dict(color='red')), row=1, col=1)

# Add predictions
fig.add_trace(
    go.Scatter(x=analysis['Predictions'].index, 
              y=analysis['Predictions'],
              name='Predicted LTC',
              line=dict(color='green', dash='dash')), row=1, col=1)

# Plot 2: Z_t Analysis
z_t = analysis['Z_t']
z_mean = analysis['Z_mean']
z_std = analysis['Z_std']

fig.add_trace(
    go.Scatter(y=z_t, name='Z_t (Spread)',
              line=dict(color='blue')), row=2, col=1)

# Add mean line
fig.add_trace(
    go.Scatter(y=[z_mean] * len(z_t), name='Mean',
              line=dict(color='black', dash='dash')), row=2, col=1)

# Add standard deviation bands
for n_std in [1, 2]:
    fig.add_trace(
        go.Scatter(y=[z_mean + z_std * n_std] * len(z_t),
                  name=f'+{n_std}σ',
                  line=dict(color='red', dash='dot')), row=2, col=1)
    fig.add_trace(
        go.Scatter(y=[z_mean - z_std * n_std] * len(z_t),
                  name=f'-{n_std}σ',
                  line=dict(color='red', dash='dot')), row=2, col=1)

# Update layout
title_text = (
    f"Pair Analysis: LTC-USDT vs BTC-USDT<br>" +
    f"P-Value: {analysis['P-Value']:.4f} | " +
    f"Current Z-Score: {analysis['Current_Z_score']:.2f} | " +
    f"Strategy: {analysis['Strategy']}<br>" +
    f"Signal Strength: {analysis['Signal_Strength']:.2f} | " +
    f"Mean Reversion Prob: {analysis['Mean_Reversion_Probability']:.2%}"
)

fig.update_layout(
    title=title_text,
    width=1200,
    height=800,
    showlegend=True
)

fig.show()
#%%
from scipy import stats
from datetime import timedelta

def generate_grid_levels(analysis, current_price, time_limit_hours=24):
    """
    Generate grid trading levels based on Z-scores.
    
    Z-score thresholds:
    - Entry: ±1.5σ
    - Target: 0 (mean reversion)
    - Stop: ±2.5σ
    """
    z_score = analysis['Current_Z_score']
    z_mean = analysis['Z_mean']
    z_std = analysis['Z_std']
    beta = analysis['Position_Ratio']
    
    # Time parameters
    current_time = analysis['Actual_Values'].index[-1]
    time_limit = current_time + timedelta(hours=time_limit_hours)
    
    # Calculate price levels based on Z-scores
    if z_score > 1.5:  # Short signal
        entry_price = current_price
        target_price = current_price * (1 - abs(z_score - 0) * z_std * beta)  # Mean reversion target
        stop_price = current_price * (1 + abs(z_score + 1) * z_std * beta)    # Additional deviation
        
        grid = {
            'strategy': 'Short',
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_price': stop_price,
            'time_limit': time_limit,
            'entry_z_score': z_score,
            'target_z_score': 0,
            'stop_z_score': z_score + 1
        }
        
    elif z_score < -1.5:  # Long signal
        entry_price = current_price
        target_price = current_price * (1 + abs(z_score - 0) * z_std * beta)  # Mean reversion target
        stop_price = current_price * (1 - abs(z_score - 1) * z_std * beta)    # Additional deviation
        
        grid = {
            'strategy': 'Long',
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_price': stop_price,
            'time_limit': time_limit,
            'entry_z_score': z_score,
            'target_z_score': 0,
            'stop_z_score': z_score - 1
        }
        
    else:  # No signal
        grid = {
            'strategy': 'Hold',
            'entry_price': None,
            'target_price': None,
            'stop_price': None,
            'time_limit': None,
            'entry_z_score': z_score,
            'target_z_score': None,
            'stop_z_score': None
        }
    
    return grid

# Get the specific candles
btc_candle = next(candle for candle in candles if candle.trading_pair == 'BTC-USDT')
ltc_candle = next(candle for candle in candles if candle.trading_pair == 'LTC-USDT')

# Get normalized price series
btc_prices = btc_candle.data["close"].pct_change().add(1).cumprod()
ltc_prices = ltc_candle.data["close"].pct_change().add(1).cumprod()

# Perform analysis
analysis = analyze_pair_cointegration(ltc_prices, btc_prices, lookback_days=14, signal_days=14, z_score_threshold=2.0)

# Generate grid levels
current_ltc_price = ltc_candle.data["close"].iloc[-1]
grid = generate_grid_levels(analysis, current_ltc_price)

# Print analysis results
print(f"\nAnalysis: LTC-USDT vs BTC-USDT")
print(f"Strategy: {analysis['Strategy']}")
print(f"P-Value: {analysis['P-Value']:.4f}")
print(f"Z-Score: {analysis['Current_Z_score']:.2f}")
print(f"Signal Strength: {analysis['Signal_Strength']:.2f}")
print(f"Mean Reversion Probability: {analysis['Mean_Reversion_Probability']:.2%}")
print(f"Position Ratio (β): {analysis['Position_Ratio']:.4f}")

# Print grid parameters if there's a signal
if grid['strategy'] != 'Hold':
    print(f"\nGrid Trading Parameters:")
    print(f"Strategy: {grid['strategy']}")
    print(f"Entry Price: {grid['entry_price']:.2f}")
    print(f"Target Price: {grid['target_price']:.2f}")
    print(f"Stop Price: {grid['stop_price']:.2f}")
    print(f"Time Limit: {grid['time_limit']}")
    print(f"Entry Z-score: {grid['entry_z_score']:.2f}")
    print(f"Target Z-score: {grid['target_z_score']:.2f}")
    print(f"Stop Z-score: {grid['stop_z_score']:.2f}")

# Create visualization with grid levels
fig = make_subplots(rows=2, cols=1, 
                    subplot_titles=('Normalized Prices and Prediction', 'Z_t (Spread)'),
                    vertical_spacing=0.15,
                    row_heights=[0.6, 0.4])

# Plot 1: Prices and Prediction
fig.add_trace(
    go.Scatter(x=ltc_prices.index, y=ltc_prices, 
              name='LTC-USDT',
              line=dict(color='blue')), row=1, col=1)
fig.add_trace(
    go.Scatter(x=btc_prices.index, y=btc_prices, 
              name='BTC-USDT',
              line=dict(color='red')), row=1, col=1)

# Add predictions
fig.add_trace(
    go.Scatter(x=analysis['Predictions'].index, 
              y=analysis['Predictions'],
              name='Predicted LTC',
              line=dict(color='green', dash='dash')), row=1, col=1)

# Add grid levels if there's a signal
if grid['strategy'] != 'Hold':
    last_timestamp = ltc_prices.index[-1]
    grid_timestamps = [last_timestamp, grid['time_limit']]
    
    # Entry level
    fig.add_trace(
        go.Scatter(x=grid_timestamps, 
                  y=[grid['entry_price'], grid['entry_price']],
                  name='Entry Level',
                  line=dict(color='yellow', dash='dash')), row=1, col=1)
    
    # Target level
    fig.add_trace(
        go.Scatter(x=grid_timestamps, 
                  y=[grid['target_price'], grid['target_price']],
                  name='Target Level',
                  line=dict(color='green', dash='dash')), row=1, col=1)
    
    # Stop level
    fig.add_trace(
        go.Scatter(x=grid_timestamps, 
                  y=[grid['stop_price'], grid['stop_price']],
                  name='Stop Level',
                  line=dict(color='red', dash='dash')), row=1, col=1)

# Plot 2: Z_t Analysis
z_t = analysis['Z_t']
z_mean = analysis['Z_mean']
z_std = analysis['Z_std']

fig.add_trace(
    go.Scatter(y=z_t, name='Z_t (Spread)',
              line=dict(color='blue')), row=2, col=1)

# Add mean line
fig.add_trace(
    go.Scatter(y=[z_mean] * len(z_t), name='Mean',
              line=dict(color='black', dash='dash')), row=2, col=1)

# Add standard deviation bands and signal thresholds
for n_std in [1, 1.5, 2, 2.5]:  # Added 1.5 and 2.5 for entry and stop levels
    fig.add_trace(
        go.Scatter(y=[z_mean + z_std * n_std] * len(z_t),
                  name=f'+{n_std}σ',
                  line=dict(color='red', dash='dot')), row=2, col=1)
    fig.add_trace(
        go.Scatter(y=[z_mean - z_std * n_std] * len(z_t),
                  name=f'-{n_std}σ',
                  line=dict(color='red', dash='dot')), row=2, col=1)

# Update layout
grid_status = f"Grid Active: {grid['strategy']}" if grid['strategy'] != 'Hold' else "No Active Grid"
title_text = (
    f"Pair Analysis: LTC-USDT vs BTC-USDT<br>" +
    f"P-Value: {analysis['P-Value']:.4f} | " +
    f"Current Z-Score: {analysis['Current_Z_score']:.2f} | " +
    f"Strategy: {analysis['Strategy']}<br>" +
    f"Signal Strength: {analysis['Signal_Strength']:.2f} | " +
    f"Mean Reversion Prob: {analysis['Mean_Reversion_Probability']:.2%}<br>" +
    f"{grid_status}"
)

fig.update_layout(
    title=title_text,
    width=1200,
    height=800,
    showlegend=True
)

fig.show()
#%%
