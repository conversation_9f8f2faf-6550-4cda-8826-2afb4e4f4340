from decimal import Decimal
from typing import List, Dict, Optional, Set, Tu<PERSON>, Union
from enum import Enum
from dataclasses import dataclass
import numpy as np
from sklearn.linear_model import LinearRegression

from hummingbot.core.data_type.common import OrderType, PositionAction, PositionMode, PriceType, TradeType
from hummingbot.data_feed.candles_feed.data_types import CandlesConfig
from hummingbot.strategy_v2.controllers import Controller<PERSON><PERSON>, ControllerConfigBase
from hummingbot.strategy_v2.executors.data_types import ConnectorPair, PositionSummary
from hummingbot.strategy_v2.executors.order_executor.data_types import ExecutionStrategy, OrderExecutorConfig
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig, TripleBarrierConfig
from hummingbot.strategy_v2.models.executor_actions import CreateExecutorAction, ExecutorAction, StopExecutorAction


class TradingState(Enum):
    """State machine states for the trading strategy"""
    IDLE = "idle"                           # No positions, waiting for signal
    OPENING_POSITIONS = "opening_positions" # Creating initial positions
    POSITIONS_ACTIVE = "positions_active"   # Positions established, monitoring time limits
    REBALANCING = "rebalancing"            # Force rebalancing to theoretical positions
    CLOSING_ALL = "closing_all"            # Closing all positions (TP/SL or time limit)
    COOLDOWN = "cooldown"                  # Waiting after close before allowing new trades


@dataclass
class StateContext:
    """Context data for state transitions"""
    first_fill_time: Optional[float] = None
    position_established_time: Optional[float] = None
    rebalance_triggered_time: Optional[float] = None
    close_triggered_time: Optional[float] = None
    close_reason: Optional[str] = None  # 'tp_sl', 'position_time_limit', 'signal_change'
    

class StatArbConfig(ControllerConfigBase):
    """
    Configuration for a statistical arbitrage controller that trades two cointegrated assets.
    """
    controller_type: str = "generic"
    controller_name: str = "stat_arb_refactor"
    candles_config: List[CandlesConfig] = []
    connector_pair_dominant: ConnectorPair = ConnectorPair(connector_name="binance_perpetual", trading_pair="SOL-USDT")
    connector_pair_hedge: ConnectorPair = ConnectorPair(connector_name="binance_perpetual", trading_pair="POPCAT-USDT")
    interval: str = "1m"
    lookback_period: int = 300
    entry_threshold: Decimal = Decimal("2.0")
    tp_global: Decimal = Decimal("0.015")
    sl_global: Decimal = Decimal("0.03")
    min_amount_quote: Decimal = Decimal("10")
    total_amount_quote: Decimal = Decimal("100")
    quoter_spread: Decimal = Decimal("0.0001")
    quoter_cooldown: int = 30
    quoter_refresh: int = 10
    max_orders_placed_per_side: int = 2
    max_orders_filled_per_side: int = 2
    max_position_deviation: Decimal = Decimal("0.1")
    pos_hedge_ratio: Decimal = Decimal("1.0")
    leverage: int = 20
    position_mode: PositionMode = PositionMode.HEDGE
    # Time limit parameters
    executor_time_limit: Optional[int] = None  # Time from first fill to force rebalance
    position_time_limit: Optional[int] = None  # Time from position establishment to close all
    open_order_type: OrderType = OrderType.LIMIT_MAKER
    cooldown_after_close: int = 60  # Cooldown period after closing all positions

    @property
    def triple_barrier_config(self) -> TripleBarrierConfig:
        return TripleBarrierConfig(
            open_order_type=OrderType.LIMIT_MAKER,
        )

    def update_markets(self, markets: dict) -> dict:
        """Update markets dictionary with both trading pairs"""
        # Add dominant pair
        if self.connector_pair_dominant.connector_name not in markets:
            markets[self.connector_pair_dominant.connector_name] = set()
        markets[self.connector_pair_dominant.connector_name].add(self.connector_pair_dominant.trading_pair)

        # Add hedge pair
        if self.connector_pair_hedge.connector_name not in markets:
            markets[self.connector_pair_hedge.connector_name] = set()
        markets[self.connector_pair_hedge.connector_name].add(self.connector_pair_hedge.trading_pair)

        return markets


class StatArb(ControllerBase):
    """
    Statistical arbitrage controller using state machine pattern.
    """

    def __init__(self, config: StatArbConfig, *args, **kwargs):
        super().__init__(config, *args, **kwargs)
        self.config = config
        self.theoretical_dominant_quote = self.config.total_amount_quote * (1 / (1 + self.config.pos_hedge_ratio))
        self.theoretical_hedge_quote = self.config.total_amount_quote * (self.config.pos_hedge_ratio / (1 + self.config.pos_hedge_ratio))

        # State machine
        self._state = TradingState.IDLE
        self._state_context = StateContext()
        
        # Tracking data
        self._executor_fill_times = {}  # {executor_id: first_fill_timestamp}

        # Initialize processed data dictionary
        self.processed_data = {
            "dominant_price": None,
            "hedge_price": None,
            "spread": None,
            "z_score": None,
            "hedge_ratio": None,
            "position_dominant": Decimal("0"),
            "position_hedge": Decimal("0"),
            "active_orders_dominant": [],
            "active_orders_hedge": [],
            "pair_pnl": Decimal("0"),
            "signal": 0  # 0: no signal, 1: long dominant/short hedge, -1: short dominant/long hedge
        }

        # Setup candles config if not already set
        if len(self.config.candles_config) == 0:
            max_records = self.config.lookback_period + 20  # extra records for safety
            self.max_records = max_records
            self.config.candles_config = [
                CandlesConfig(
                    connector=self.config.connector_pair_dominant.connector_name,
                    trading_pair=self.config.connector_pair_dominant.trading_pair,
                    interval=self.config.interval,
                    max_records=max_records
                ),
                CandlesConfig(
                    connector=self.config.connector_pair_hedge.connector_name,
                    trading_pair=self.config.connector_pair_hedge.trading_pair,
                    interval=self.config.interval,
                    max_records=max_records
                )
            ]
        if "_perpetual" in self.config.connector_pair_dominant.connector_name:
            connector = self.market_data_provider.get_connector(self.config.connector_pair_dominant.connector_name)
            connector.set_position_mode(self.config.position_mode)
            connector.set_leverage(self.config.connector_pair_dominant.trading_pair, self.config.leverage)
        if "_perpetual" in self.config.connector_pair_hedge.connector_name:
            connector = self.market_data_provider.get_connector(self.config.connector_pair_hedge.connector_name)
            connector.set_position_mode(self.config.position_mode)
            connector.set_leverage(self.config.connector_pair_hedge.trading_pair, self.config.leverage)

    @property
    def state(self) -> TradingState:
        """Current state of the trading strategy"""
        return self._state
    
    def transition_to(self, new_state: TradingState, reason: Optional[str] = None):
        """Transition to a new state with logging"""
        old_state = self._state
        self._state = new_state
        
        if reason:
            self.logger().info(f"State transition: {old_state.value} -> {new_state.value} (reason: {reason})")
        else:
            self.logger().info(f"State transition: {old_state.value} -> {new_state.value}")
            
        # Handle state entry actions
        self._on_state_entry(new_state, reason)
    
    def _on_state_entry(self, state: TradingState, reason: Optional[str] = None):
        """Handle actions when entering a new state"""
        current_time = self.market_data_provider.time()
        
        if state == TradingState.IDLE:
            # Reset all tracking when entering IDLE
            self._state_context = StateContext()
            self._executor_fill_times.clear()
            
        elif state == TradingState.OPENING_POSITIONS:
            # Starting to open positions
            pass
            
        elif state == TradingState.POSITIONS_ACTIVE:
            # Positions are now active
            if self._state_context.position_established_time is None:
                self._state_context.position_established_time = current_time
                
        elif state == TradingState.REBALANCING:
            # Starting rebalance
            self._state_context.rebalance_triggered_time = current_time
            
        elif state == TradingState.CLOSING_ALL:
            # Closing all positions
            self._state_context.close_triggered_time = current_time
            self._state_context.close_reason = reason
            
        elif state == TradingState.COOLDOWN:
            # Entering cooldown period
            pass

    def update_state_from_market_conditions(self):
        """Update state based on current market conditions and positions"""
        current_time = self.market_data_provider.time()
        has_positions = self.has_any_positions()
        has_active_executors = self.has_active_executors()
        
        # State-specific transitions
        if self._state == TradingState.IDLE:
            if self.processed_data["signal"] != 0 and not has_positions:
                self.transition_to(TradingState.OPENING_POSITIONS, "signal_detected")
                
        elif self._state == TradingState.OPENING_POSITIONS:
            if self.are_positions_established():
                self.transition_to(TradingState.POSITIONS_ACTIVE, "positions_established")
                
        elif self._state == TradingState.POSITIONS_ACTIVE:
            # Check time limits
            if self.check_position_time_limit():
                self.transition_to(TradingState.CLOSING_ALL, "position_time_limit")
            elif self.check_executor_time_limit():
                self.transition_to(TradingState.REBALANCING, "executor_time_limit")
            elif self.check_tp_sl_hit():
                self.transition_to(TradingState.CLOSING_ALL, "tp_sl")
                
        elif self._state == TradingState.REBALANCING:
            # Check if rebalance is complete
            if self.is_rebalance_complete():
                self.transition_to(TradingState.POSITIONS_ACTIVE, "rebalance_complete")
                
        elif self._state == TradingState.CLOSING_ALL:
            # Check if all positions are closed
            if not has_positions and not has_active_executors:
                self.transition_to(TradingState.COOLDOWN, "all_closed")
                
        elif self._state == TradingState.COOLDOWN:
            # Check if cooldown period is over
            if self._state_context.close_triggered_time:
                time_since_close = current_time - self._state_context.close_triggered_time
                if time_since_close >= self.config.cooldown_after_close:
                    self.transition_to(TradingState.IDLE, "cooldown_complete")

    def determine_executor_actions(self) -> List[ExecutorAction]:
        """
        Main execution logic using state machine pattern.
        """
        # Update state based on current conditions
        self.update_state_from_market_conditions()
        
        # Track order fills
        self.track_order_fills()
        
        # Execute state-specific actions
        actions = []
        
        if self._state == TradingState.IDLE:
            # No actions in IDLE state
            pass
            
        elif self._state == TradingState.OPENING_POSITIONS:
            # Open new positions based on signal
            actions.extend(self.get_executors_to_quote())
            
        elif self._state == TradingState.POSITIONS_ACTIVE:
            # Maintain positions, refresh orders
            actions.extend(self.get_executors_to_quote())
            actions.extend(self.get_executors_to_keep_position())
            actions.extend(self.get_executors_to_refresh())
            
        elif self._state == TradingState.REBALANCING:
            # Execute force rebalance
            actions.extend(self.execute_force_rebalance())
            
        elif self._state == TradingState.CLOSING_ALL:
            # Close all positions
            actions.extend(self.execute_close_all_positions())
            
        elif self._state == TradingState.COOLDOWN:
            # No actions during cooldown
            pass
            
        return actions

    def has_any_positions(self) -> bool:
        """Check if there are any open positions"""
        return len(self.positions_held) > 0

    def has_active_executors(self) -> bool:
        """Check if there are any active executors"""
        return any(executor.is_active for executor in self.executors_info)

    def are_positions_established(self) -> bool:
        """Check if both dominant and hedge positions have been established"""
        _, executors_dominant_filled = self.get_executors_dominant()
        _, executors_hedge_filled = self.get_executors_hedge()
        
        return len(executors_dominant_filled) > 0 and len(executors_hedge_filled) > 0

    def is_rebalance_complete(self) -> bool:
        """Check if rebalance has brought positions close to theoretical"""
        current_positions = self.calculate_current_positions()
        
        # Calculate theoretical positions
        theoretical_dominant = self.theoretical_dominant_quote / self.processed_data["dominant_price"]
        theoretical_hedge = self.theoretical_hedge_quote / self.processed_data["hedge_price"]
        
        # Check if positions are within 5% of theoretical
        dominant_close = False
        hedge_close = False
        
        if current_positions["dominant_amount"] != Decimal("0"):
            dominant_deviation = abs(current_positions["dominant_amount"] - theoretical_dominant) / theoretical_dominant
            dominant_close = dominant_deviation < Decimal("0.05")
            
        if current_positions["hedge_amount"] != Decimal("0"):
            hedge_deviation = abs(abs(current_positions["hedge_amount"]) - theoretical_hedge) / theoretical_hedge
            hedge_close = hedge_deviation < Decimal("0.05")
            
        return dominant_close and hedge_close

    def check_tp_sl_hit(self) -> bool:
        """Check if take profit or stop loss has been hit"""
        pnl_pct = self.processed_data.get("pair_pnl_pct", Decimal("0"))
        return pnl_pct > self.config.tp_global or pnl_pct < -self.config.sl_global

    def track_order_fills(self):
        """Track first fill timestamps for executor time limit"""
        current_time = self.market_data_provider.time()
        
        for executor in self.executors_info:
            if not executor.is_active:
                continue
                
            executor_id = executor.id
            
            # Track first fill time
            if executor_id not in self._executor_fill_times:
                if executor.filled_amount_quote > 0:
                    self._executor_fill_times[executor_id] = current_time
                    self.logger().info(f"First fill recorded for executor {executor_id} at {current_time}")
                    
                    # Update context if this is the first fill overall
                    if self._state_context.first_fill_time is None:
                        self._state_context.first_fill_time = current_time

    def check_executor_time_limit(self) -> bool:
        """Check if any executor has exceeded time limit since first fill"""
        if not self.config.executor_time_limit or not self._state_context.first_fill_time:
            return False
            
        current_time = self.market_data_provider.time()
        time_since_first_fill = current_time - self._state_context.first_fill_time
        
        return time_since_first_fill > self.config.executor_time_limit

    def check_position_time_limit(self) -> bool:
        """Check if position has exceeded holding time limit"""
        if not self.config.position_time_limit or not self._state_context.position_established_time:
            return False
            
        current_time = self.market_data_provider.time()
        time_since_establishment = current_time - self._state_context.position_established_time
        
        return time_since_establishment > self.config.position_time_limit

    def execute_close_all_positions(self) -> List[ExecutorAction]:
        """Close all positions and stop all executors"""
        self.logger().info(f"Closing all positions (reason: {self._state_context.close_reason})")
        actions = []
        
        # Close all positions using OrderExecutor
        for position in self.positions_held:
            actions.extend(self.get_executors_to_reduce_position(position))
            
        # Stop all active executors
        actions.extend(self.stop_all_executors())
        
        return actions

    def execute_force_rebalance(self) -> List[ExecutorAction]:
        """Force rebalance positions to theoretical values"""
        self.logger().info("Executing force rebalance to theoretical positions")
        actions = []
        
        # Stop all active executors first
        actions.extend(self.stop_all_executors())
        
        # Calculate current positions
        current_positions = self.calculate_current_positions()
        
        # Get current prices
        dominant_price, hedge_price = self.get_pairs_prices()
        
        # Calculate gaps and create rebalance orders
        current_time = self.market_data_provider.time()
        
        gap_data = {
            "dominant": {
                "gap": self.theoretical_dominant_quote - current_positions["dominant_quote"],
                "side": current_positions["dominant_side"],
                "connector_pair": self.config.connector_pair_dominant,
                "price": Decimal(str(dominant_price))
            },
            "hedge": {
                "gap": self.theoretical_hedge_quote - current_positions["hedge_quote"],
                "side": current_positions["hedge_side"],
                "connector_pair": self.config.connector_pair_hedge,
                "price": Decimal(str(hedge_price))
            }
        }
        
        for gap_type, data in gap_data.items():
            gap_value = data["gap"]
            position_side = data["side"]
            
            if abs(gap_value) >= self.config.min_amount_quote:
                connector_pair = data["connector_pair"]
                price = data["price"]
                
                # Determine trade side and position action
                buy_side = (gap_value > 0 and position_side == TradeType.BUY) or (gap_value < 0 and position_side == TradeType.SELL)
                side = TradeType.BUY if buy_side else TradeType.SELL
                position_action = PositionAction.OPEN if gap_value > 0 else PositionAction.CLOSE
                amount = abs(gap_value) / price
                
                config = OrderExecutorConfig(
                    timestamp=current_time,
                    connector_name=connector_pair.connector_name,
                    trading_pair=connector_pair.trading_pair,
                    side=side,
                    amount=amount,
                    position_action=position_action,
                    execution_strategy=ExecutionStrategy.MARKET,
                    price=price
                )
                
                self.logger().info(f"Force rebalance for {gap_type}: Gap={gap_value}, Side={side}, Amount={amount:.8f}")
                
                actions.append(CreateExecutorAction(
                    controller_id=self.config.id,
                    executor_config=config
                ))
                
        return actions

    def get_executors_to_quote(self) -> List[ExecutorAction]:
        """
        Get Order Executor to quote from the dominant and hedge markets.
        Only creates orders in appropriate states.
        """
        actions: List[ExecutorAction] = []
        
        # Only create orders in specific states
        if self._state not in [TradingState.OPENING_POSITIONS, TradingState.POSITIONS_ACTIVE]:
            return actions
            
        trade_type_dominant = TradeType.BUY if self.processed_data["signal"] == 1 else TradeType.SELL
        trade_type_hedge = TradeType.SELL if self.processed_data["signal"] == 1 else TradeType.BUY

        # Get current number of placed orders
        dominant_placed_count = len(self.processed_data["executors_dominant_placed"])
        hedge_placed_count = len(self.processed_data["executors_hedge_placed"])

        # Analyze dominant active orders
        if self.processed_data["dominant_gap"] > Decimal("0") and \
           self.processed_data["filter_connector_pair"] != self.config.connector_pair_dominant and \
           dominant_placed_count < self.config.max_orders_placed_per_side and \
           len(self.processed_data["executors_dominant_filled"]) < self.config.max_orders_filled_per_side:
            
            # Use order book price
            order_position = dominant_placed_count
            if order_position < len(self.processed_data["dominant_order_book_prices"]):
                base_price = self.processed_data["dominant_order_book_prices"][order_position]
            else:
                base_price = self.processed_data["dominant_price"]
            
            # Apply spread
            if trade_type_dominant == TradeType.BUY:
                price = Decimal(base_price) * (1 - self.config.quoter_spread)
            else:
                price = Decimal(base_price) * (1 + self.config.quoter_spread)
                
            dominant_executor_config = PositionExecutorConfig(
                timestamp=self.market_data_provider.time(),
                connector_name=self.config.connector_pair_dominant.connector_name,
                trading_pair=self.config.connector_pair_dominant.trading_pair,
                side=trade_type_dominant,
                entry_price=price,
                amount=self.config.min_amount_quote / self.processed_data["dominant_price"],
                triple_barrier_config=self.config.triple_barrier_config,
                leverage=self.config.leverage,
            )
            actions.append(CreateExecutorAction(controller_id=self.config.id, executor_config=dominant_executor_config))

        # Analyze hedge active orders
        if self.processed_data["hedge_gap"] > Decimal("0") and \
           self.processed_data["filter_connector_pair"] != self.config.connector_pair_hedge and \
           hedge_placed_count < self.config.max_orders_placed_per_side and \
           len(self.processed_data["executors_hedge_filled"]) < self.config.max_orders_filled_per_side:
            
            # Use order book price
            order_position = hedge_placed_count
            if order_position < len(self.processed_data["hedge_order_book_prices"]):
                base_price = self.processed_data["hedge_order_book_prices"][order_position]
            else:
                base_price = self.processed_data["hedge_price"]
                
            # Apply spread
            if trade_type_hedge == TradeType.BUY:
                price = Decimal(base_price) * (1 - self.config.quoter_spread)
            else:
                price = Decimal(base_price) * (1 + self.config.quoter_spread)
                
            hedge_executor_config = PositionExecutorConfig(
                timestamp=self.market_data_provider.time(),
                connector_name=self.config.connector_pair_hedge.connector_name,
                trading_pair=self.config.connector_pair_hedge.trading_pair,
                side=trade_type_hedge,
                entry_price=price,
                amount=self.config.min_amount_quote / self.processed_data["hedge_price"],
                triple_barrier_config=self.config.triple_barrier_config,
                leverage=self.config.leverage,
            )
            actions.append(CreateExecutorAction(controller_id=self.config.id, executor_config=hedge_executor_config))
            
        return actions

    def stop_all_executors(self) -> List[StopExecutorAction]:
        """Stop all active executors"""
        return [
            StopExecutorAction(
                controller_id=self.config.id,
                executor_id=executor.id,
                keep_position=False
            )
            for executor in self.executors_info
            if executor.is_active
        ]

    def calculate_current_positions(self) -> Dict[str, Decimal]:
        """Calculate total current positions including active executors"""
        # Get static positions
        position_dominant = next((p for p in self.positions_held if p.connector_name == self.config.connector_pair_dominant.connector_name and p.trading_pair == self.config.connector_pair_dominant.trading_pair), None)
        position_hedge = next((p for p in self.positions_held if p.connector_name == self.config.connector_pair_hedge.connector_name and p.trading_pair == self.config.connector_pair_hedge.trading_pair), None)

        # Calculate quote amounts
        dominant_quote = position_dominant.amount_quote if position_dominant else Decimal("0")
        hedge_quote = position_hedge.amount_quote if position_hedge else Decimal("0")

        return {
            "dominant_quote": dominant_quote,
            "hedge_quote": hedge_quote,
            "dominant_side": position_dominant.side if position_dominant else None,
            "hedge_side": position_hedge.side if position_hedge else None,
            "dominant_amount": position_dominant.amount if position_dominant else Decimal("0"),
            "hedge_amount": position_hedge.amount if position_hedge else Decimal("0")
        }

    def get_pairs_prices(self) -> Tuple[float, float]:
        """Get best bid/ask prices from mid-price"""
        dominant_mid_price = self.market_data_provider.get_price_by_type(
            self.config.connector_pair_dominant.connector_name,
            self.config.connector_pair_dominant.trading_pair,
            PriceType.MidPrice
        )
        hedge_mid_price = self.market_data_provider.get_price_by_type(
            self.config.connector_pair_hedge.connector_name,
            self.config.connector_pair_hedge.trading_pair,
            PriceType.MidPrice
        )
        return dominant_mid_price, hedge_mid_price

    def get_order_book_prices(self, connector_name: str, trading_pair: str, side: TradeType, levels: int = 3) -> List[Decimal]:
        """Get order book prices for dynamic order placement"""
        order_book = self.market_data_provider.get_order_book(connector_name, trading_pair)
        
        if not order_book:
            # Fallback to mid price if order book not available
            mid_price = self.market_data_provider.get_price_by_type(
                connector_name, trading_pair, PriceType.MidPrice
            )
            return [Decimal(str(mid_price))] * levels
        
        prices = []
        if side == TradeType.BUY:
            # Get best ask prices (prices to buy at)
            asks = list(order_book.ask_entries())
            for i in range(min(levels, len(asks))):
                prices.append(asks[i].price)
        else:
            # Get best bid prices (prices to sell at)
            bids = list(order_book.bid_entries())
            for i in range(min(levels, len(bids))):
                prices.append(bids[i].price)
        
        # Fill remaining levels with last price if not enough levels
        if len(prices) < levels and prices:
            last_price = prices[-1]
            while len(prices) < levels:
                prices.append(last_price)
        
        return prices

    def get_executors_dominant(self) -> Tuple[List, List]:
        """Get dominant executors separated by placed and filled"""
        placed = self.filter_executors(
            self.executors_info,
            filter_func=lambda e: e.is_active and e.filled_amount_quote == 0 and
            e.connector_name == self.config.connector_pair_dominant.connector_name and
            e.trading_pair == self.config.connector_pair_dominant.trading_pair
        )
        filled = self.filter_executors(
            self.executors_info,
            filter_func=lambda e: e.is_active and e.filled_amount_quote > 0 and
            e.connector_name == self.config.connector_pair_dominant.connector_name and
            e.trading_pair == self.config.connector_pair_dominant.trading_pair
        )
        return placed, filled

    def get_executors_hedge(self) -> Tuple[List, List]:
        """Get hedge executors separated by placed and filled"""
        placed = self.filter_executors(
            self.executors_info,
            filter_func=lambda e: e.is_active and e.filled_amount_quote == 0 and
            e.connector_name == self.config.connector_pair_hedge.connector_name and
            e.trading_pair == self.config.connector_pair_hedge.trading_pair
        )
        filled = self.filter_executors(
            self.executors_info,
            filter_func=lambda e: e.is_active and e.filled_amount_quote > 0 and
            e.connector_name == self.config.connector_pair_hedge.connector_name and
            e.trading_pair == self.config.connector_pair_hedge.trading_pair
        )
        return placed, filled

    def get_executors_to_keep_position(self) -> List[ExecutorAction]:
        stop_actions: List[ExecutorAction] = []
        for executor in self.processed_data["executors_dominant_filled"] + self.processed_data["executors_hedge_filled"]:
            if self.market_data_provider.time() - executor.timestamp >= self.config.quoter_cooldown:
                # Create a new executor to keep the position
                stop_actions.append(StopExecutorAction(controller_id=self.config.id, executor_id=executor.id, keep_position=True))
        return stop_actions

    def get_executors_to_refresh(self) -> List[ExecutorAction]:
        refresh_actions: List[ExecutorAction] = []
        for executor in self.processed_data["executors_dominant_placed"] + self.processed_data["executors_hedge_placed"]:
            if self.market_data_provider.time() - executor.timestamp >= self.config.quoter_refresh:
                # Create a new executor to refresh the position
                refresh_actions.append(StopExecutorAction(controller_id=self.config.id, executor_id=executor.id, keep_position=False))
        return refresh_actions

    def get_executors_to_reduce_position(self, position: PositionSummary) -> List[ExecutorAction]:
        """
        Get Order Executor to reduce position
        """
        actions: List[ExecutorAction] = []
        reduce_config = OrderExecutorConfig(
            timestamp=self.market_data_provider.time(),
            connector_name=position.connector_name,
            trading_pair=position.trading_pair,
            side=TradeType.SELL if position.side == TradeType.BUY else TradeType.BUY,
            position_action=PositionAction.CLOSE,
            execution_strategy=ExecutionStrategy.MARKET,
            amount=position.amount,
            price=self.market_data_provider.get_price_by_type(
                position.connector_name,
                position.trading_pair,
                PriceType.MidPrice
            )
        )
        actions.append(CreateExecutorAction(controller_id=self.config.id, executor_config=reduce_config))
        return actions

    async def update_processed_data(self):
        """
        Update processed data with the latest market information and statistical calculations
        needed for the statistical arbitrage strategy.
        """
        # Stat arb analysis
        spread, z_score = self.get_spread_and_z_score()

        # Generate trading signal based on z-score
        entry_threshold = float(self.config.entry_threshold)
        if z_score > entry_threshold:
            # Spread is too high, expect it to revert: long dominant, short hedge
            signal = 1
            dominant_side, hedge_side = TradeType.BUY, TradeType.SELL
        elif z_score < -entry_threshold:
            # Spread is too low, expect it to revert: short dominant, long hedge
            signal = -1
            dominant_side, hedge_side = TradeType.SELL, TradeType.BUY
        else:
            # No signal
            signal = 0
            dominant_side, hedge_side = None, None

        # Current prices
        dominant_price, hedge_price = self.get_pairs_prices()

        # Get current positions stats by signal
        positions_dominant = next((position for position in self.positions_held if position.connector_name == self.config.connector_pair_dominant.connector_name and position.trading_pair == self.config.connector_pair_dominant.trading_pair and (position.side == dominant_side or dominant_side is None)), None)
        positions_hedge = next((position for position in self.positions_held if position.connector_name == self.config.connector_pair_hedge.connector_name and position.trading_pair == self.config.connector_pair_hedge.trading_pair and (position.side == hedge_side or hedge_side is None)), None)
        # Get position stats
        position_dominant_quote = positions_dominant.amount_quote if positions_dominant else Decimal("0")
        position_hedge_quote = positions_hedge.amount_quote if positions_hedge else Decimal("0")
        position_dominant_pnl_quote = positions_dominant.global_pnl_quote if positions_dominant else Decimal("0")
        position_hedge_pnl_quote = positions_hedge.global_pnl_quote if positions_hedge else Decimal("0")
        pair_pnl_pct = (position_dominant_pnl_quote + position_hedge_pnl_quote) / (position_dominant_quote + position_hedge_quote) if (position_dominant_quote + position_hedge_quote) != 0 else Decimal("0")
        # Get active executors
        executors_dominant_placed, executors_dominant_filled = self.get_executors_dominant()
        executors_hedge_placed, executors_hedge_filled = self.get_executors_hedge()
        
        # Get order book prices for better order placement
        dominant_order_book_prices = self.get_order_book_prices(
            self.config.connector_pair_dominant.connector_name,
            self.config.connector_pair_dominant.trading_pair,
            dominant_side if dominant_side else TradeType.BUY,
            self.config.max_orders_placed_per_side
        )
        hedge_order_book_prices = self.get_order_book_prices(
            self.config.connector_pair_hedge.connector_name,
            self.config.connector_pair_hedge.trading_pair,
            hedge_side if hedge_side else TradeType.BUY,
            self.config.max_orders_placed_per_side
        )

        active_amount_dominant = Decimal(str(sum([executor.filled_amount_quote for executor in executors_dominant_filled])))
        active_amount_hedge = Decimal(str(sum([executor.filled_amount_quote for executor in executors_hedge_filled])))

        # Compute imbalance based on the hedge ratio
        dominant_gap = self.theoretical_dominant_quote - position_dominant_quote - active_amount_dominant
        hedge_gap = self.theoretical_hedge_quote - position_hedge_quote - active_amount_hedge
        imbalance = position_dominant_quote - position_hedge_quote
        imbalance_scaled = position_dominant_quote - position_hedge_quote * self.config.pos_hedge_ratio
        imbalance_scaled_pct = imbalance_scaled / position_dominant_quote if position_dominant_quote != Decimal("0") else Decimal("0")
        filter_connector_pair = None
        if imbalance_scaled_pct > self.config.max_position_deviation:
            # Avoid placing orders in the dominant market
            filter_connector_pair = self.config.connector_pair_dominant
        elif imbalance_scaled_pct < -self.config.max_position_deviation:
            # Avoid placing orders in the hedge market
            filter_connector_pair = self.config.connector_pair_hedge

        # Update processed data
        self.processed_data.update({
            "dominant_price": Decimal(str(dominant_price)),
            "hedge_price": Decimal(str(hedge_price)),
            "spread": Decimal(str(spread)),
            "z_score": Decimal(str(z_score)),
            "dominant_gap": Decimal(str(dominant_gap)),
            "hedge_gap": Decimal(str(hedge_gap)),
            "position_dominant_quote": position_dominant_quote,
            "position_hedge_quote": position_hedge_quote,
            "active_amount_dominant": active_amount_dominant,
            "active_amount_hedge": active_amount_hedge,
            "signal": signal,
            # Store full dataframes for reference
            "imbalance": Decimal(str(imbalance)),
            "imbalance_scaled_pct": Decimal(str(imbalance_scaled_pct)),
            "filter_connector_pair": filter_connector_pair,
            "dominant_order_book_prices": dominant_order_book_prices,
            "hedge_order_book_prices": hedge_order_book_prices,
            "executors_dominant_filled": executors_dominant_filled,
            "executors_hedge_filled": executors_hedge_filled,
            "executors_dominant_placed": executors_dominant_placed,
            "executors_hedge_placed": executors_hedge_placed,
            "pair_pnl_pct": pair_pnl_pct,
        })

    def get_spread_and_z_score(self):
        # Fetch candle data for both assets
        dominant_df = self.market_data_provider.get_candles_df(
            connector_name=self.config.connector_pair_dominant.connector_name,
            trading_pair=self.config.connector_pair_dominant.trading_pair,
            interval=self.config.interval,
            max_records=self.max_records
        )

        hedge_df = self.market_data_provider.get_candles_df(
            connector_name=self.config.connector_pair_hedge.connector_name,
            trading_pair=self.config.connector_pair_hedge.trading_pair,
            interval=self.config.interval,
            max_records=self.max_records
        )

        if dominant_df.empty or hedge_df.empty:
            return 0, 0
        # Get the close prices
        dominant_close = dominant_df['close'].values[-self.config.lookback_period:]
        hedge_close = hedge_df['close'].values[-self.config.lookback_period:]

        # Ensure both have the same length
        min_length = min(len(dominant_close), len(hedge_close))
        dominant_close = dominant_close[-min_length:]
        hedge_close = hedge_close[-min_length:]

        # Calculate hedge ratio using linear regression
        X = dominant_close.reshape(-1, 1)
        y = hedge_close
        model = LinearRegression()
        model.fit(X, y)
        hedge_ratio = model.coef_[0]

        # Calculate spread and z-score
        spread = hedge_close - hedge_ratio * dominant_close
        spread_mean = np.mean(spread)
        spread_std = np.std(spread)

        if spread_std > 0:
            z_score = (spread[-1] - spread_mean) / spread_std
        else:
            z_score = 0

        return spread[-1], z_score