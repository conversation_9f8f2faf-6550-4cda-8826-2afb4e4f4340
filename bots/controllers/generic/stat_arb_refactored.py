class StatArbRefactored(ControllerBase):
    def __init__(self, config: StatArbConfig, *args, **kwargs):
        super().__init__(config, *args, **kwargs)
        self.config = config
        
        # 组件初始化
        self.state_machine = TradingStateMachine()
        self.time_tracker = TimeTracker()
        self.position_manager = PositionManager(config)
        self.signal_analyzer = SignalAnalyzer(config)
        self.executor_factory = ExecutorConfigFactory()
        
        # 策略映射
        self.execution_strategies = {
            TradingState.CLOSING_ALL: TimeBasedCloseStrategy(),
            TradingState.REBALANCING: ForceRebalanceStrategy(),
            TradingState.OPENING_POSITIONS: OpenPositionsStrategy(),
        }
    
    def determine_executor_actions(self) -> List[ExecutorAction]:
        # 更新状态
        self.state_machine.update_state(self.get_market_context())
        
        # 获取当前状态对应的策略
        current_state = self.state_machine.current_state
        strategy = self.execution_strategies.get(current_state)
        
        if strategy:
            return strategy.execute(self.get_execution_context())
        
        return []