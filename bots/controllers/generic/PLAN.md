# StatArb Executor Time Limit & Position Time Limit 需求计划

## 1. 我对现有系统的理解

### 1.1 StatArb控制器架构
```
StatArb Controller
    ├── 管理两个配对资产的套利交易
    ├── 使用z-score判断进出场信号
    ├── 维护理论仓位（theoretical_dominant_quote/hedge_quote）
    └── 通过创建PositionExecutor来执行交易
```

### 1.2 Executor生命周期
```
1. CreateExecutorAction → 创建PositionExecutor
2. PositionExecutor执行:
   - 下限价单（LIMIT_MAKER）
   - 订单成交后 → is_trading = True
   - 达到止盈/止损/时间限制 → 平仓
3. StopExecutorAction → 停止Executor
   - keep_position=True: 保留仓位，仅停止Executor
   - keep_position=False: 停止Executor并平仓
```

### 1.3 仓位管理机制
- `positions_held`: 当前持有的所有仓位列表
- `executors_info`: 所有Executor的信息列表
- `processed_data`: 实时计算的交易数据（gap、仓位、价格等）

### 1.4 时间追踪现状
- 每个Executor有`timestamp`属性（创建时间）
- ExecutorInfo有`custom_info`字典可存储自定义数据
- 目前没有系统级的仓位时间追踪机制

## 2. 功能需求理解

### 2.1 executor_time_limit（执行器时限）
**目的**: 当Executor运行超过指定时间后，强制平衡仓位到理论值

**触发条件**:
- 有活跃信号（signal != 0）
- 存在最早的Executor时间
- 当前时间 - 最早Executor时间 >= executor_time_limit

**执行动作**:
1. 停止所有未成交的Executor（is_active && !is_trading）
2. 计算理论仓位差值（theoretical_gap）
3. 创建市价单来补齐差值

### 2.2 position_time_limit（持仓时限）
**目的**: 当持仓时间超过限制后，平掉所有仓位

**触发条件**:
- 存在持仓（positions_held不为空）
- 当前时间 - 最早持仓时间 >= position_time_limit

**执行动作**:
1. 停止所有活跃的Executor
2. 对每个持仓创建市价平仓单

## 3. 实现方案设计

### 3.1 时间追踪改进方案

#### 方案A：使用ExecutorInfo.custom_info
```python
# 在订单成交时记录
executor.custom_info['held_position_orders'] = [
    {
        'order_id': order.order_id,
        'creation_timestamp': order.creation_timestamp,
        'filled_timestamp': current_time,
        'amount': order.executed_amount_base
    }
]
```

#### 方案B：Controller级别的时间管理
```python
class StatArb(ControllerBase):
    def __init__(self, ...):
        # 新增时间追踪
        self._position_start_times = {}  # {(connector, trading_pair, side): timestamp}
        self._executor_start_time = None  # 第一个Executor创建时间
```

### 3.2 状态管理方案
```python
# 防止重复触发的状态标记
self._executor_limit_triggered = False
self._position_limit_triggered = False
self._last_signal = 0

# 状态重置条件
if signal != self._last_signal:
    self._executor_limit_triggered = False
    self._last_signal = signal
```

## 4. 具体实现疑问

### 4.1 关于executor_time_limit

**Q1**: executor_time_limit的计时起点应该是？
- A. 第一个Executor创建时间？
- B. 最早的成交订单时间？
- C. 信号产生的时间？
- B

**Q2**: 强制平衡时，如果理论gap小于min_amount_quote怎么处理？
- A. 忽略这个差值？
- B. 等待累积到min_amount_quote？
- C. 使用特殊逻辑处理小额订单？
- C
你先看看源码中有没有更好的最佳实践 如果没有比如目前gap为5 min_amount_quote为10 那么我可以先买10 然后gap变为-5 然后平掉5 这种方案比较通用
或者
有更好的方案是以binance_perpetual为例 hummingbot代码中可以获得该币种的最小下单值 即使我设置了min_amount_quote=10但是如果 该币种的最小下单值为5 那么我可以直接买5个以达到平衡 默认gap<1则忽略

**Q3**: 强制平衡后，如果信号仍然存在，是否应该：
- A. 继续创建新的Executor？
- B. 暂停一段时间（cooldown）？
- C. 等待信号改变？
- 如果已经强制平衡且达到理论仓位 忽略信号等到自动触发止盈止损货仓位时限平仓即可

**Q4**: 对于部分成交的订单（如示例中的657/714），时间如何计算？
- A. 使用订单创建时间？
- B. 使用第一笔成交时间？
- C. 使用订单完全成交时间？
- 你先看看源码中有没有更好的最佳实践取得成交的时间 B

### 4.2 关于position_time_limit

**Q5**: position_time_limit的计时起点应该是？
- A. 第一笔成交的时间？
- B. 仓位建立完成的时间？
- C. 每个方向（dominant/hedge）分别计时？
- 你先看看源码中有没有更好的最佳实践取得仓位建立的时间 B

**Q6**: 如果dominant和hedge的建仓时间不同，如何处理？
- A. 使用最早的时间？
- B. 分别计时，分别处理？
- C. 使用加权平均时间？
如dominant仓位在15:33 建仓完成hedge仓位在16:00建仓完成 则以该整个的对冲仓位作为时间
该例子中16:00建仓完全完成 则最后到达position_time_limit后 平仓

**Q7**: 平仓时的优先级如何确定？
- A. 同时平掉所有仓位？
- B. 先平亏损仓位？
- C. 先平盈利仓位？
A 使用MARKET平仓所有
### 4.3 边界情况处理

**Q8**: 如果在executor_time_limit触发时，部分Executor正在成交中，如何处理？
- A. 等待成交完成再计算gap？
- B. 立即计算并可能导致过量？
- C. 取消正在成交的订单？
- 观察hummingbot源码中是否考虑了类似的场景 他是如何做的

**Q9**: 如果强制平衡的市价单金额过大（如日志中的3087 SHIB），如何处理？
- A. 设置最大单笔订单限制？
- B. 分批执行？
- C. 使用限价单代替？
- 观察hummingbot源码中是否考虑了类似的场景 他是如何做的
**Q10**: 网络延迟或交易所API问题导致订单状态不同步时，如何保证时间计算准确？
- 观察hummingbot源码中是否考虑了类似的场景 他是如何做的
### 4.4 与现有逻辑的集成

**Q11**: executor_time_limit和position_time_limit的优先级关系？
- 示例：如果两个时限同时到达，先执行哪个？
先检查position_time_limit 再检查executor_time_limit 最后执行原有的逻辑 

**Q12**: 与现有的止盈止损（tp_global/sl_global）逻辑如何协调？
- 优先级顺序是什么？
检查position_time_limit -> 再检查executor_time_limit 最后执行原有的逻辑

**Q13**: 强制操作是否需要记录在日志或数据库中以便分析？
暂不需要 打印相关日志在控制台即可
## 5. 性能和安全考虑

### 5.1 性能优化
- 时间检查是否需要每个tick都执行？
- 需要
- 是否需要缓存计算结果避免重复计算？
- 暂不需要

### 5.2 安全保护
- 如何防止因价格计算错误导致的异常大额订单？
- 是否需要设置最大仓位偏差保护？
- 市价单滑点保护？
暂不考虑 看看目前的代码实现了吗 或者hummingbot有哪些相关的最佳实践
## 6. 测试场景设计

### 6.1 正常场景
1. executor_time_limit正常触发并平衡
2. position_time_limit正常触发并平仓
3. 两个限制的协同工作

### 6.2 边界场景
1. 时限到达时恰好信号改变
2. 强制平衡时遇到最小订单限制
3. 网络中断后的恢复
4. 部分成交的处理

### 6.3 异常场景
1. 价格剧烈波动
2. 流动性不足
3. 交易所API限制

## 7. 实现优先级建议

1. **Phase 1**: 基础功能实现
   - 时间追踪机制
   - executor_time_limit基本逻辑
   - position_time_limit基本逻辑

2. **Phase 2**: 边界处理
   - 小额订单处理
   - 状态管理优化
   - 错误恢复机制

3. **Phase 3**: 性能优化
   - 缓存优化
   - 批量处理
   - 监控和告警

## 8. 请您确认的关键决策

1. **时间计算方式**：请明确两个time_limit的计时起点
2. **状态管理策略**：触发后的行为（继续/暂停/等待）
3. **优先级关系**：各种限制条件的执行顺序
4. **异常处理策略**：对于计算错误、订单失败等情况的处理方式
5. **具体参数范围**：建议的默认值和合理范围

---

请您在上述疑问点给出明确的需求说明，我将据此在stat_arb_dev.py中实现完整功能。