from typing import Dict, Optional, List
from abc import ABC, abstractmethod

class TimeObserver(ABC):
    @abstractmethod
    def on_executor_time_limit_reached(self):
        pass
    
    @abstractmethod
    def on_position_time_limit_reached(self):
        pass

class TimeTracker:
    def __init__(self, config):
        self.config = config
        self._first_fill_timestamps: Dict[str, float] = {}
        self._position_established_time: Optional[float] = None
        self._observers: List[TimeObserver] = []
        self._executor_limit_triggered = False
        self._position_limit_triggered = False
    
    def add_observer(self, observer: TimeObserver):
        self._observers.append(observer)
    
    def track_first_fill(self, executor_id: str, timestamp: float):
        if executor_id not in self._first_fill_timestamps:
            self._first_fill_timestamps[executor_id] = timestamp
    
    def track_position_established(self, timestamp: float):
        if self._position_established_time is None:
            self._position_established_time = timestamp
    
    def check_executor_time_limit(self, current_time: float) -> bool:
        if (self.config.executor_time_limit is None or 
            self._executor_limit_triggered or 
            not self._first_fill_timestamps):
            return False
        
        earliest_fill = min(self._first_fill_timestamps.values())
        return current_time - earliest_fill >= self.config.executor_time_limit
    
    def check_position_time_limit(self, current_time: float) -> bool:
        if (self.config.position_time_limit is None or 
            self._position_limit_triggered or 
            self._position_established_time is None):
            return False
        
        return current_time - self._position_established_time >= self.config.position_time_limit
    
    def reset_tracking(self):
        self._first_fill_timestamps.clear()
        self._position_established_time = None
        self._executor_limit_triggered = False
        self._position_limit_triggered = False
    
    def set_executor_limit_triggered(self):
        self._executor_limit_triggered = True
    
    def set_position_limit_triggered(self):
        self._position_limit_triggered = True