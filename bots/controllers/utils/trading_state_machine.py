from typing import Dict, Callable
from .trading_state import TradingState, StateContext

class TradingStateMachine:
    def __init__(self):
        self.current_state = TradingState.IDLE
        self.context = StateContext()
        
        # 状态转换映射
        self.transitions: Dict[TradingState, Dict[str, TradingState]] = {
            TradingState.IDLE: {
                "signal_detected": TradingState.OPENING_POSITIONS
            },
            TradingState.OPENING_POSITIONS: {
                "positions_established": TradingState.POSITIONS_ACTIVE,
                "timeout": TradingState.IDLE
            },
            TradingState.POSITIONS_ACTIVE: {
                "position_time_limit": TradingState.CLOSING_ALL,
                "executor_time_limit": TradingState.REBALANCING,
                "tp_sl_hit": TradingState.CLOSING_ALL,
                "signal_changed": TradingState.IDLE
            },
            TradingState.REBALANCING: {
                "rebalance_complete": TradingState.POSITIONS_ACTIVE,
                "tp_sl_hit": TradingState.CLOSING_ALL
            },
            TradingState.CLOSING_ALL: {
                "all_closed": TradingState.COOLDOWN
            },
            TradingState.COOLDOWN: {
                "cooldown_complete": TradingState.IDLE
            }
        }
    
    def transition_to(self, new_state: TradingState, trigger: str = None):
        """状态转换"""
        if self.can_transition_to(new_state, trigger):
            old_state = self.current_state
            self.current_state = new_state
            self.on_state_changed(old_state, new_state, trigger)
    
    def can_transition_to(self, new_state: TradingState, trigger: str = None) -> bool:
        """检查是否可以转换到新状态"""
        if self.current_state not in self.transitions:
            return False
        
        valid_transitions = self.transitions[self.current_state]
        return trigger in valid_transitions and valid_transitions[trigger] == new_state
    
    def on_state_changed(self, old_state: TradingState, new_state: TradingState, trigger: str):
        """状态改变时的回调"""
        print(f"State transition: {old_state.value} -> {new_state.value} (trigger: {trigger})")
    
    def update_state(self, market_context):
        """根据市场条件更新状态"""
        current_time = market_context.current_time
        
        if self.current_state == TradingState.IDLE:
            if market_context.signal != 0 and not market_context.has_positions:
                self.transition_to(TradingState.OPENING_POSITIONS, "signal_detected")
        
        elif self.current_state == TradingState.OPENING_POSITIONS:
            if market_context.positions_established:
                self.transition_to(TradingState.POSITIONS_ACTIVE, "positions_established")
        
        elif self.current_state == TradingState.POSITIONS_ACTIVE:
            if market_context.position_time_limit_reached:
                self.transition_to(TradingState.CLOSING_ALL, "position_time_limit")
            elif market_context.executor_time_limit_reached:
                self.transition_to(TradingState.REBALANCING, "executor_time_limit")
            elif market_context.tp_sl_hit:
                self.transition_to(TradingState.CLOSING_ALL, "tp_sl_hit")
        
        elif self.current_state == TradingState.REBALANCING:
            if market_context.tp_sl_hit:
                self.transition_to(TradingState.CLOSING_ALL, "tp_sl_hit")
            elif market_context.rebalance_complete:
                self.transition_to(TradingState.POSITIONS_ACTIVE, "rebalance_complete")
        
        elif self.current_state == TradingState.CLOSING_ALL:
            if not market_context.has_positions and not market_context.has_active_executors:
                self.transition_to(TradingState.COOLDOWN, "all_closed")
        
        elif self.current_state == TradingState.COOLDOWN:
            # 简单的冷却逻辑，可以根据需要扩展
            self.transition_to(TradingState.IDLE, "cooldown_complete")