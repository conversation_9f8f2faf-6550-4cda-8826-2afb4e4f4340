from enum import Enum
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal

class TradingState(Enum):
    IDLE = "idle"
    OPENING_POSITIONS = "opening_positions"
    POSITIONS_ACTIVE = "positions_active"
    REBALANCING = "rebalancing"
    CLOSING_ALL = "closing_all"
    COOLDOWN = "cooldown"

@dataclass
class StateContext:
    entry_time: Optional[float] = None
    first_fill_time: Optional[float] = None
    position_established_time: Optional[float] = None
    last_signal: int = 0
    executor_limit_triggered: bool = False
    position_limit_triggered: bool = False