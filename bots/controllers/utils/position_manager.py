from typing import Dict, List, Tuple
from decimal import Decimal
from hummingbot.core.data_type.common import TradeType

class PositionManager:
    def __init__(self, config):
        self.config = config
        self.theoretical_dominant_quote = config.total_amount_quote * (1 / (1 + config.pos_hedge_ratio))
        self.theoretical_hedge_quote = config.total_amount_quote * (config.pos_hedge_ratio / (1 + config.pos_hedge_ratio))
    
    def calculate_current_positions(self, positions_held, executors_info) -> Dict[str, Decimal]:
        # 获取静态仓位
        position_dominant = next((p for p in positions_held 
                                if p.connector_name == self.config.connector_pair_dominant.connector_name 
                                and p.trading_pair == self.config.connector_pair_dominant.trading_pair), None)
        position_hedge = next((p for p in positions_held 
                             if p.connector_name == self.config.connector_pair_hedge.connector_name 
                             and p.trading_pair == self.config.connector_pair_hedge.trading_pair), None)
        
        dominant_quote = position_dominant.amount_quote if position_dominant else Decimal("0")
        hedge_quote = position_hedge.amount_quote if position_hedge else Decimal("0")
        
        # 计算活跃订单金额
        active_amount_dominant = sum(
            executor.filled_amount_quote for executor in executors_info
            if (executor.is_active and 
                executor.connector_name == self.config.connector_pair_dominant.connector_name and
                executor.trading_pair == self.config.connector_pair_dominant.trading_pair)
        )
        
        active_amount_hedge = sum(
            executor.filled_amount_quote for executor in executors_info
            if (executor.is_active and 
                executor.connector_name == self.config.connector_pair_hedge.connector_name and
                executor.trading_pair == self.config.connector_pair_hedge.trading_pair)
        )
        
        return {
            "dominant_quote": dominant_quote,
            "hedge_quote": hedge_quote,
            "active_amount_dominant": active_amount_dominant,
            "active_amount_hedge": active_amount_hedge,
            "dominant_amount": dominant_quote / self.config.connector_pair_dominant.trading_pair if dominant_quote else Decimal("0"),
            "hedge_amount": hedge_quote / self.config.connector_pair_hedge.trading_pair if hedge_quote else Decimal("0")
        }
    
    def calculate_gaps(self, current_positions: Dict[str, Decimal]) -> Dict[str, Decimal]:
        dominant_gap = (self.theoretical_dominant_quote - 
                       current_positions["dominant_quote"] - 
                       current_positions["active_amount_dominant"])
        
        hedge_gap = (self.theoretical_hedge_quote - 
                    current_positions["hedge_quote"] - 
                    current_positions["active_amount_hedge"])
        
        return {
            "dominant_gap": dominant_gap,
            "hedge_gap": hedge_gap
        }
    
    def calculate_imbalance(self, current_positions: Dict[str, Decimal]) -> Dict[str, Decimal]:
        imbalance = current_positions["dominant_quote"] - current_positions["hedge_quote"]
        imbalance_scaled = (current_positions["dominant_quote"] - 
                           current_positions["hedge_quote"] * self.config.pos_hedge_ratio)
        imbalance_scaled_pct = (imbalance_scaled / current_positions["dominant_quote"] 
                               if current_positions["dominant_quote"] != Decimal("0") else Decimal("0"))
        
        return {
            "imbalance": imbalance,
            "imbalance_scaled_pct": imbalance_scaled_pct
        }
    
    def check_positions_established(self, executors_dominant_filled, executors_hedge_filled) -> bool:
        return len(executors_dominant_filled) > 0 and len(executors_hedge_filled) > 0