from abc import ABC, abstractmethod
from typing import List
from hummingbot.strategy_v2.models.executor_actions import ExecutorAction, CreateExecutorAction, StopExecutorAction
from hummingbot.core.data_type.common import TradeType, PositionAction
from hummingbot.strategy_v2.executors.order_executor.data_types import ExecutionStrategy

class ExecutionStrategy(ABC):
    @abstractmethod
    def execute(self, context) -> List[ExecutorAction]:
        pass

class TimeBasedCloseStrategy(ExecutionStrategy):
    def execute(self, context) -> List[ExecutorAction]:
        """关闭所有仓位的策略"""
        actions = []
        
        # 关闭所有仓位
        for position in context.positions_held:
            reduce_config = context.executor_factory.create_order_executor(
                connector_pair=context.get_connector_pair_for_position(position),
                side=TradeType.SELL if position.side == TradeType.BUY else TradeType.BUY,
                amount=position.amount,
                position_action=PositionAction.CLOSE,
                price=context.get_mid_price(position.connector_name, position.trading_pair),
                current_time=context.current_time,
                execution_strategy=ExecutionStrategy.MARKET
            )
            actions.append(CreateExecutorAction(
                controller_id=context.controller_id,
                executor_config=reduce_config
            ))
        
        # 停止所有活跃执行器
        for executor in context.executors_info:
            if executor.is_active:
                actions.append(StopExecutorAction(
                    controller_id=context.controller_id,
                    executor_id=executor.id
                ))
        
        return actions

class ForceRebalanceStrategy(ExecutionStrategy):
    def execute(self, context) -> List[ExecutorAction]:
        """强制再平衡策略"""
        actions = []
        
        # 停止所有活跃执行器
        for executor in context.executors_info:
            if executor.is_active:
                actions.append(StopExecutorAction(
                    controller_id=context.controller_id,
                    executor_id=executor.id
                ))
        
        # 计算当前仓位和缺口
        current_positions = context.position_manager.calculate_current_positions(
            context.positions_held, context.executors_info
        )
        gaps = context.position_manager.calculate_gaps(current_positions)
        
        # 获取当前价格
        dominant_price = context.get_mid_price(
            context.config.connector_pair_dominant.connector_name,
            context.config.connector_pair_dominant.trading_pair
        )
        hedge_price = context.get_mid_price(
            context.config.connector_pair_hedge.connector_name,
            context.config.connector_pair_hedge.trading_pair
        )
        
        # 处理主导资产缺口
        if abs(gaps["dominant_gap"]) >= context.config.min_amount_quote:
            side = TradeType.BUY if gaps["dominant_gap"] > 0 else TradeType.SELL
            position_action = PositionAction.OPEN if gaps["dominant_gap"] > 0 else PositionAction.CLOSE
            amount = abs(gaps["dominant_gap"]) / dominant_price
            
            config = context.executor_factory.create_order_executor(
                connector_pair=context.config.connector_pair_dominant,
                side=side,
                amount=amount,
                position_action=position_action,
                price=dominant_price,
                current_time=context.current_time
            )
            
            actions.append(CreateExecutorAction(
                controller_id=context.controller_id,
                executor_config=config
            ))
        
        # 处理对冲资产缺口
        if abs(gaps["hedge_gap"]) >= context.config.min_amount_quote:
            side = TradeType.BUY if gaps["hedge_gap"] > 0 else TradeType.SELL
            position_action = PositionAction.OPEN if gaps["hedge_gap"] > 0 else PositionAction.CLOSE
            amount = abs(gaps["hedge_gap"]) / hedge_price
            
            config = context.executor_factory.create_order_executor(
                connector_pair=context.config.connector_pair_hedge,
                side=side,
                amount=amount,
                position_action=position_action,
                price=hedge_price,
                current_time=context.current_time
            )
            
            actions.append(CreateExecutorAction(
                controller_id=context.controller_id,
                executor_config=config
            ))
        
        return actions

class OpenPositionsStrategy(ExecutionStrategy):
    def execute(self, context) -> List[ExecutorAction]:
        """开仓策略"""
        return context.get_executors_to_quote()

class MaintainPositionsStrategy(ExecutionStrategy):
    def execute(self, context) -> List[ExecutorAction]:
        """维持仓位策略"""
        actions = []
        actions.extend(context.get_executors_to_quote())
        actions.extend(context.get_executors_to_keep_position())
        actions.extend(context.get_executors_to_refresh())
        return actions