import numpy as np
from sklearn.linear_model import LinearRegression
from typing import <PERSON>ple
from hummingbot.core.data_type.common import TradeType

class SignalAnalyzer:
    def __init__(self, config):
        self.config = config
    
    def analyze_spread_and_signal(self, market_data_provider) -> Tuple[float, float, int, TradeType, TradeType]:
        spread, z_score = self.get_spread_and_z_score(market_data_provider)
        
        entry_threshold = float(self.config.entry_threshold)
        if z_score > entry_threshold:
            signal = 1
            dominant_side, hedge_side = TradeType.BUY, TradeType.SELL
        elif z_score < -entry_threshold:
            signal = -1
            dominant_side, hedge_side = TradeType.SELL, TradeType.BUY
        else:
            signal = 0
            dominant_side, hedge_side = None, None
        
        return spread, z_score, signal, dominant_side, hedge_side
    
    def get_spread_and_z_score(self, market_data_provider) -> Tuple[float, float]:
        # 获取K线数据
        dominant_df = market_data_provider.get_candles_df(
            connector_name=self.config.connector_pair_dominant.connector_name,
            trading_pair=self.config.connector_pair_dominant.trading_pair,
            interval=self.config.interval,
            max_records=500
        )
        
        hedge_df = market_data_provider.get_candles_df(
            connector_name=self.config.connector_pair_hedge.connector_name,
            trading_pair=self.config.connector_pair_hedge.trading_pair,
            interval=self.config.interval,
            max_records=500
        )
        
        if dominant_df.empty or hedge_df.empty:
            return 0, 0
        
        # 获取收盘价
        dominant_close = dominant_df['close'].values[-self.config.lookback_period:]
        hedge_close = hedge_df['close'].values[-self.config.lookback_period:]
        
        # 确保长度一致
        min_length = min(len(dominant_close), len(hedge_close))
        dominant_close = dominant_close[-min_length:]
        hedge_close = hedge_close[-min_length:]
        
        # 计算对冲比率
        X = dominant_close.reshape(-1, 1)
        y = hedge_close
        model = LinearRegression()
        model.fit(X, y)
        hedge_ratio = model.coef_[0]
        
        # 计算价差和z分数
        spread = hedge_close - hedge_ratio * dominant_close
        spread_mean = np.mean(spread)
        spread_std = np.std(spread)
        
        if spread_std == 0:
            return spread[-1], 0
        
        z_score = (spread[-1] - spread_mean) / spread_std
        return spread[-1], z_score