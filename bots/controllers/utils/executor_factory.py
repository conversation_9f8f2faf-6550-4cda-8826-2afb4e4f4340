from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig
from hummingbot.strategy_v2.executors.order_executor.data_types import OrderExecutorConfig, ExecutionStrategy
from hummingbot.core.data_type.common import OrderType, PositionAction

class ExecutorConfigFactory:
    @staticmethod
    def create_position_executor(config, connector_pair, side, price, amount, current_time):
        return PositionExecutorConfig(
            timestamp=current_time,
            connector_name=connector_pair.connector_name,
            trading_pair=connector_pair.trading_pair,
            side=side,
            entry_price=price,
            amount=amount,
            triple_barrier_config=config.triple_barrier_config,
            leverage=config.leverage,
        )
    
    @staticmethod
    def create_order_executor(connector_pair, side, amount, position_action, price, current_time, execution_strategy=ExecutionStrategy.MARKET):
        return OrderExecutorConfig(
            timestamp=current_time,
            connector_name=connector_pair.connector_name,
            trading_pair=connector_pair.trading_pair,
            side=side,
            amount=amount,
            position_action=position_action,
            execution_strategy=execution_strategy,
            price=price
        )